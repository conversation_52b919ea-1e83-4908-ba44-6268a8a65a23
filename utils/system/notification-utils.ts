import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';
import { useUserStore } from '@/store/user/user-store';

// Configure notification handler to show alerts when app is in foreground
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldPlaySound: true,
    shouldSetBadge: true,
    shouldShowBanner: true,
    shouldShowList: true,
  }),
});

export async function registerForPushNotificationsAsync() {

  if (Platform.OS === 'android') {
    Notifications.setNotificationChannelAsync('default', {
      name: 'default',
      importance: Notifications.AndroidImportance.MAX,
      vibrationPattern: [0, 250, 250, 250],
      lightColor: '#FF231F7C',
    });
  }

  const { status: existingStatus } = await Notifications.getPermissionsAsync();
  let finalStatus = existingStatus;
  if (existingStatus !== 'granted') {
    const { status } = await Notifications.requestPermissionsAsync();
    finalStatus = status;
  }
  if (finalStatus !== 'granted') {
    alert('Failed to get push token for push notification!');
    return;
  }
  const token = (await Notifications.getExpoPushTokenAsync()).data;
  console.log(token);

  return token;
}

export async function scheduleDailyCheckinNotification() {
  const profile = useUserStore.getState().profile;
  const dailyRemindersEnabled = profile?.notificationSettings?.dailyReminders;
  const reminderTime = profile?.notificationSettings?.dailyReminderTime || "20:00";

  await Notifications.cancelAllScheduledNotificationsAsync(); // Clear existing daily notifications

  if (dailyRemindersEnabled) {
    // Parse the time string (HH:MM format)
    const [hours, minutes] = reminderTime.split(':').map(Number);

    await Notifications.scheduleNotificationAsync({
      content: {
        title: profile?.language === 'nl' ? "Tijd voor je dagelijkse check-in!" : "Time for your daily check-in!",
        body: profile?.language === 'nl' ? 'Vergeet niet je voortgang bij te houden en je stemming te loggen.' : 'Don\'t forget to track your progress and log your mood.',
        data: { type: 'daily-checkin' },
      },
      trigger: {
        channelId: 'default', // Channel ID should be here for Android
        hour: hours,
        minute: minutes,
        repeats: true,
      } as Notifications.NotificationTriggerInput, // Explicitly cast to the correct type
    });

    // Format time for display (12-hour format for user feedback)
    const displayTime = new Date();
    displayTime.setHours(hours, minutes);
    const timeString = displayTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

    console.log(`Daily check-in notification scheduled for ${timeString}.`);
  } else {
    console.log("Daily check-in reminders are disabled. No notification scheduled.");
  }
}

export async function cancelAllNotifications() {
  await Notifications.cancelAllScheduledNotificationsAsync();
  console.log("All scheduled notifications cancelled.");
}