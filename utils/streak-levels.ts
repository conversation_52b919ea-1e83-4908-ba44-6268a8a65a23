import React from 'react';
import {
  Calendar,
  Heart,
  Target,
  Flame,
  Trophy,
  Award,
} from 'lucide-react-native';

export interface StreakLevel {
  level: string;
  color: string;
  icon: React.ComponentType<{ size?: number; color?: string; strokeWidth?: number }>;
  minDays: number;
  maxDays?: number;
}

export const STREAK_LEVELS: StreakLevel[] = [
  {
    level: 'starting',
    color: '#2196F3',
    icon: Calendar,
    minDays: 0,
    maxDays: 6,
  },
  {
    level: 'building',
    color: '#4CAF50',
    icon: Heart,
    minDays: 7,
    maxDays: 29,
  },
  {
    level: 'warm',
    color: '#FF9800',
    icon: Target,
    minDays: 30,
    maxDays: 89,
  },
  {
    level: 'hot',
    color: '#FF5722',
    icon: Flame,
    minDays: 90,
    maxDays: 179,
  },
  {
    level: 'epic',
    color: '#9C27B0',
    icon: Trophy,
    minDays: 180,
    maxDays: 364,
  },
  {
    level: 'legendary',
    color: '#FFD700',
    icon: Award,
    minDays: 365,
  },
];

export const getStreakLevel = (days: number): StreakLevel => {
  for (let i = STREAK_LEVELS.length - 1; i >= 0; i--) {
    const level = STREAK_LEVELS[i];
    if (days >= level.minDays && (level.maxDays === undefined || days <= level.maxDays)) {
      return level;
    }
  }
  return STREAK_LEVELS[0]; // Default to starting level
};

export const getStreakLevelName = (days: number, language: string = 'en'): string => {
  const level = getStreakLevel(days);
  
  if (language === 'nl') {
    const translations: Record<string, string> = {
      starting: 'Beginnend',
      building: 'Opbouwend',
      warm: 'Warm',
      hot: 'Heet',
      epic: 'Episch',
      legendary: 'Legendarisch',
    };
    return translations[level.level] || level.level;
  }
  
  return level.level.charAt(0).toUpperCase() + level.level.slice(1);
};

export const getStreakLevelShort = (days: number, language: string = 'en'): string => {
  const level = getStreakLevel(days);
  
  if (language === 'nl') {
    const translations: Record<string, string> = {
      starting: 'Start',
      building: 'Build',
      warm: 'Warm',
      hot: 'Heet',
      epic: 'Epic',
      legendary: 'Leg',
    };
    return translations[level.level] || level.level.slice(0, 3);
  }
  
  return level.level.charAt(0).toUpperCase() + level.level.slice(1, 3);
};

export const isLegendaryLevel = (days: number): boolean => {
  return days >= 365;
};

export const isEpicLevel = (days: number): boolean => {
  return days >= 180 && days < 365;
};

export const getNextStreakLevel = (days: number): StreakLevel | null => {
  const currentLevel = getStreakLevel(days);
  const currentIndex = STREAK_LEVELS.findIndex(level => level.level === currentLevel.level);
  
  if (currentIndex < STREAK_LEVELS.length - 1) {
    return STREAK_LEVELS[currentIndex + 1];
  }
  
  return null; // Already at max level
};

export const getDaysToNextLevel = (days: number): number => {
  const nextLevel = getNextStreakLevel(days);
  if (!nextLevel) return 0;
  
  return nextLevel.minDays - days;
};

export const getStreakProgress = (days: number): number => {
  const currentLevel = getStreakLevel(days);
  const nextLevel = getNextStreakLevel(days);
  
  if (!nextLevel) return 1; // Already at max level
  
  const levelRange = nextLevel.minDays - currentLevel.minDays;
  const currentProgress = days - currentLevel.minDays;
  
  return Math.min(currentProgress / levelRange, 1);
};

// Time breakdown utility
export interface TimeBreakdown {
  years: number;
  months: number;
  weeks: number;
  days: number;
}

export const calculateTimeBreakdown = (days: number): TimeBreakdown => {
  const years = Math.floor(days / 365);
  const remainingAfterYears = days % 365;
  const months = Math.floor(remainingAfterYears / 30);
  const remainingAfterMonths = remainingAfterYears % 30;
  const weeks = Math.floor(remainingAfterMonths / 7);
  const remainingDays = remainingAfterMonths % 7;

  return {
    years,
    months,
    weeks,
    days: remainingDays,
  };
};

export const formatTimeBreakdown = (days: number, language: string = 'en'): string => {
  const breakdown = calculateTimeBreakdown(days);
  const parts = [];

  if (breakdown.years > 0) {
    const yearText = language === 'nl' ? 'jaar' : 'year';
    const yearSuffix = breakdown.years !== 1 && language === 'en' ? 's' : '';
    parts.push(`${breakdown.years} ${yearText}${yearSuffix}`);
  }

  if (breakdown.months > 0) {
    const monthText = language === 'nl' ? 'maand' : 'month';
    const monthSuffix = breakdown.months !== 1 ? (language === 'nl' ? 'en' : 's') : '';
    parts.push(`${breakdown.months} ${monthText}${monthSuffix}`);
  }

  if (breakdown.weeks > 0) {
    const weekText = language === 'nl' ? 'week' : 'week';
    const weekSuffix = breakdown.weeks !== 1 && language === 'en' ? 's' : '';
    parts.push(`${breakdown.weeks} ${weekText}${weekSuffix}`);
  }

  if (breakdown.days > 0 || parts.length === 0) {
    const dayText = language === 'nl' ? 'dag' : 'day';
    const daySuffix = breakdown.days !== 1 ? (language === 'nl' ? 'en' : 's') : '';
    parts.push(`${breakdown.days} ${dayText}${daySuffix}`);
  }

  return parts.slice(0, 2).join(', ');
};

// Milestone utilities
export const getMilestoneProgress = (currentDays: number, milestoneDays: number): number => {
  if (currentDays >= milestoneDays) return 1;
  return Math.min(currentDays / milestoneDays, 1);
};

export const getDefaultMilestones = (language: string = 'en') => {
  const milestones = [
    { days: 1, nameEn: 'First Day', nameNl: 'Eerste Dag' },
    { days: 3, nameEn: 'Three Days', nameNl: 'Drie Dagen' },
    { days: 7, nameEn: 'One Week', nameNl: 'Eerste Week' },
    { days: 14, nameEn: 'Two Weeks', nameNl: 'Twee Weken' },
    { days: 30, nameEn: 'One Month', nameNl: 'Eerste Maand' },
    { days: 60, nameEn: 'Two Months', nameNl: 'Twee Maanden' },
    { days: 90, nameEn: 'Three Months', nameNl: 'Drie Maanden' },
    { days: 120, nameEn: 'Four Months', nameNl: 'Vier Maanden' },
    { days: 180, nameEn: 'Six Months', nameNl: 'Zes Maanden' },
    { days: 270, nameEn: 'Nine Months', nameNl: 'Negen Maanden' },
    { days: 365, nameEn: 'One Year', nameNl: 'Één Jaar' },
    { days: 730, nameEn: 'Two Years', nameNl: 'Twee Jaar' },
    { days: 1095, nameEn: 'Three Years', nameNl: 'Drie Jaar' },
    { days: 1825, nameEn: 'Five Years', nameNl: 'Vijf Jaar' },
  ];

  return milestones.map(milestone => ({
    days: milestone.days,
    name: language === 'nl' ? milestone.nameNl : milestone.nameEn,
    color: getStreakLevel(milestone.days).color,
    icon: getStreakLevel(milestone.days).icon,
    level: getStreakLevel(milestone.days).level,
  }));
};
