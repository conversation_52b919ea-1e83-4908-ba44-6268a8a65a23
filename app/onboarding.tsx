import React from "react";
import { useState, useRef, useEffect } from "react";
import {
  StyleSheet,
  View,
  ScrollView,
  Dimensions,
  Animated,
  Keyboard,
  Alert,
  Platform,
  Text,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { StatusBar } from "expo-status-bar";
import { router } from "expo-router";
import { useUserStore } from "@/store/user/user-store";
import { useTranslation } from "@/hooks/useTranslation";
import { DateTimePickerEvent } from "@react-native-community/datetimepicker";
import { useTheme } from "@/context/theme-context";
import Colors from "@/constants/colors";
import * as Haptics from "expo-haptics";
import * as DocumentPicker from "expo-document-picker";
import * as FileSystem from "expo-file-system";
import { AlertCircle } from "lucide-react-native";
import { healthGoalOptions } from "@/constants/health-goals";
// Import the new step components
import WelcomeStep from "../components/onboarding/WelcomeStep";
import LanguageStep from "../components/onboarding/LanguageStep";
import NameStep from "../components/onboarding/NameStep";
import AddictionStep from "../components/onboarding/AddictionStep";
import SobrietyStep from "../components/onboarding/SobrietyStep";
import HealthGoalsStep from "../components/onboarding/HealthGoalsStep";
import CustomizeStep from "../components/onboarding/CustomizeStep";
import RecoveryGoalStep from "../components/onboarding/RecoveryGoalStep";

const { height } = Dimensions.get("window");
const isWeb = Platform.select({ web: true, default: false });

// Default units for each addiction type
const defaultUnits = {
  Alcohol: "glasses",
  Tobacco: "cigarettes",
  Cannabis: "joints",
  Opioids: "doses",
  Stimulants: "doses",
  Gambling: "sessions",
  "Social Media": "hours",
  Other: "units",
};

export default function OnboardingScreen() {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { setProfile, importUserData } = useUserStore();
  const { t } = useTranslation();
  const [step, setStep] = useState(0);
  const [name, setName] = useState("");
  const [addiction, setAddiction] = useState("");
  const [customAddiction, setCustomAddiction] = useState("");
  const [recoveryGoalType, setRecoveryGoalType] = useState<"Abstinence" | "Harm Reduction" | undefined>(undefined);
  const [sobrietyDate, setSobrietyDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(Platform.OS === "ios");
  const [language, setLanguage] = useState<"en" | "nl">("en");
  const [usageAmount, setUsageAmount] = useState("");
  const [usageUnit, setUsageUnit] = useState("");
  const [usageCost, setUsageCost] = useState("");
  const [costFrequency, setCostFrequency] = useState("daily");
  const [dashboardView, setDashboardView] = useState<"cards" | "circular">(
    "cards"
  );
  const [errors, setErrors] = useState<{ [key: string]: string }>({});
  const [isImporting, setIsImporting] = useState(false);
  const [healthGoals, setHealthGoals] = useState(() => {
    const initialGoals: {
      [key: string]: { enabled: boolean; target: string };
    } = {};
    healthGoalOptions.forEach((goal) => {
      // Set sensible default targets based on the goal type
      let defaultTarget = "";
      switch (goal.id) {
        case "sleep":
          defaultTarget = "8";
          break;
        case "exercise":
          defaultTarget = "30";
          break;
        case "pills":
          defaultTarget = "2";
          break;
        case "waterIntake":
          defaultTarget = "8";
          break;
      }
      // Enable all health goals by default to show all 4 cards
      initialGoals[goal.id] = {
        enabled: true,
        target: defaultTarget,
      };
    });
    return initialGoals;
  });

  // Currency symbol based on language
  const [currency, setCurrency] = useState<"USD" | "EUR">(
    language === "nl" ? "EUR" : "USD"
  );

  // Update currency symbol when language changes
  useEffect(() => {
    setCurrency(language === "nl" ? "EUR" : "USD");
  }, [language]);

  // Update usage unit when addiction changes
  useEffect(() => {
    if (addiction && addiction !== "Other") {
      setUsageUnit(
        defaultUnits[addiction as keyof typeof defaultUnits] || "units"
      );
    } else if (addiction === "Other") {
      setUsageUnit("units");
    }
  }, [addiction]);

  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.9)).current;
  const scrollViewRef = useRef<ScrollView>(null);

  // Run entrance animation when step changes
  useEffect(() => {
    // Reset animations
    fadeAnim.setValue(0);
    slideAnim.setValue(50);
    scaleAnim.setValue(0.9);

    // Run animations
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();

    // Provide haptic feedback on step change
    if (!isWeb && step > 0) {
      Haptics.selectionAsync();
    }
  }, [step, fadeAnim, slideAnim, scaleAnim]);

  const validateStep = () => {
    // Clear previous errors
    setErrors({});

    // Validate current step
    switch (step) {
      case 1: // Language validation
        // No validation needed for language selection
        break;
      case 2: // Name validation
        if (!name.trim()) {
          setErrors({ name: t('onboarding.name.error') });
          return false;
        }
        break;
      case 3: // Addiction validation
        if (!addiction) {
          setErrors({ addiction: t('onboarding.addiction.error') });
          return false;
        }
        if (addiction === "Other" && !customAddiction.trim()) {
          setErrors({
            customAddiction: t('onboarding.addiction.customError'),
          });
          return false;
        }
        break;
      case 4: // Recovery Goal validation
        if (!recoveryGoalType) {
          setErrors({ recoveryGoalType: t('onboarding.recoveryGoal.error') });
          return false;
        }
        break;
      // Other steps don't have required fields
    }
    return true;
  };

  const handleNext = () => {
    // Dismiss keyboard if open
    Keyboard.dismiss();
    // Validate current step before proceeding
    if (step > 0 && !validateStep()) {
      // If validation fails, provide haptic feedback
      if (!isWeb) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
      return;
    }
    if (step < 7) {
      // Total steps: 0-7 (Welcome, Language, Name, Addiction, RecoveryGoal, Sobriety, HealthGoals, Customize)
      setStep(step + 1);
      scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
      // Success haptic feedback
      if (!isWeb) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } else {
      completeOnboarding();
    }
  };

  const handleBack = () => {
    if (step > 0) {
      setStep(step - 1);
      scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
      // Light haptic feedback
      if (!isWeb) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
      }
    }
  };

  const handleSkip = () => {
    // Skip optional information
    if (step === 5) {
      // Skip Sobriety Details
      setStep(6); // Go to Health Goals
      scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
    } else if (step === 6) {
      // Skip Health Goals
      setStep(7); // Go to Customize Display
      scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
    } else if (step === 7) {
      // Skip Customize Display
      completeOnboarding();
    }
  };

  const completeOnboarding = () => {
    // Success haptic feedback
    if (!isWeb) {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
    }
    const finalAddiction = addiction === "Other" ? customAddiction : addiction;
    // Create default emergency plans
    const documents = [
      {
        id: "emergency-plan-default",
        title: t('crisis.emergencyPlans.emergencyPlansTitle'),
        content: t('crisis.emergencyPlans.defaultPlan'),
        date: new Date().toISOString(),
        category: "EmergencyPlan",
      },
    ];
    // Determine substance type based on addiction
    let substanceType = finalAddiction;
    if (finalAddiction === "Alcohol") {
      substanceType = "Alcohol";
    } else if (finalAddiction === "Tobacco") {
      substanceType = "Cigarettes";
    } else if (finalAddiction === "Cannabis") {
      substanceType = "Cannabis";
    } else if (finalAddiction === "Opioids") {
      substanceType = "Opioids";
    } else if (finalAddiction === "Stimulants") {
      substanceType = "Stimulants";
    } else if (finalAddiction === "Gambling") {
      substanceType = "Gambling";
    } else if (finalAddiction === "Social Media") {
      substanceType = "Social Media";
    }
    // Parse usage amount as a number
    const usageQuantityNum = parseFloat(usageAmount) || 0;
    setProfile({
      name,
      addiction: finalAddiction,
      substanceType,
      recoveryGoalType,
      sobrietyDate: sobrietyDate.toISOString(),
      language,
      currency,
      usageAmount: `${usageAmount} ${usageUnit}`,
      usageQuantity: usageQuantityNum,
      usageUnit: usageUnit,
      usageCost: usageCost ? parseFloat(usageCost) : 0,
      costFrequency,
      dashboardView,
      hasCompletedOnboarding: true,
      emergencyContacts: [],
      relapses: [],
      moodEntries: [],
      dailyCheckIns: [],
      documents: documents,
      mediaFiles: [],
      customExercises: [],
      favoriteExercises: [],
      completedExercises: [],
      notificationSettings: {
        dailyReminders: true,
        dailyReminderTime: "20:00", // Default to 8 PM
        milestoneAlerts: true,
        emergencyAlerts: true,
        journalReminders: true,
        mindfulnessReminders: true,
        weeklyReports: true,
        communityMessages: false,
      },
      privacySettings: {
        dataCollection: true,
        anonymousAnalytics: true,
        crashReporting: true,
        locationTracking: false,
        biometricAuth: false,
        appLock: false,
      },
      healthMetrics: [],
      profileImage: null,
      email: "",
      phone: "",
      country: "",
      birthday: "",
      showHealthMetrics: Object.values(healthGoals).some(
        (goal) => goal.enabled
      ),
      healthGoals: Object.entries(healthGoals).map(([key, value]) => ({
        id: key,
        label: healthGoalOptions.find((opt) => opt.id === key)?.label || "",
        labelNL: healthGoalOptions.find((opt) => opt.id === key)?.labelNL || "",
        icon:
          healthGoalOptions.find((opt) => opt.id === key)?.icon || AlertCircle,
        unit: healthGoalOptions.find((opt) => opt.id === key)?.unit || "",
        unitNL: healthGoalOptions.find((opt) => opt.id === key)?.unitNL || "",
        target: value.target ? parseFloat(value.target) : null,
        enabled: value.enabled,
        currentValue: 0,
        history: [],
      })),
    });
    router.replace("/(tabs)/dashboard");
  };

  const onDateChange = (event: DateTimePickerEvent, selectedDate?: Date) => {
    if (selectedDate) {
      setSobrietyDate(selectedDate);
      if (Platform.OS === "android") {
        setShowDatePicker(false);
      }
    }
  };

  // Helper function to get personalized message
  const getPersonalizedMessage = () => {
    if (!name) return "";
    switch (step) {
      case 3:
        return language === "nl"
          ? `${name}, we zijn hier om je herstelreis te ondersteunen.`
          : `${name}, we're here to support your recovery journey.`;
      case 4:
        return language === "nl"
          ? `Wat is je hersteldoel, ${name}?`
          : `What's your recovery goal, ${name}?`;
      case 5:
        return language === "nl"
          ? `Goede keuze, ${name}! Laten we je voortgang bijhouden.`
          : `Great choice, ${name}! Let's track your progress.`;
      case 6:
        return language === "nl"
          ? `Focus op welzijn, ${name}! Stel je gezondheidsdoelen.`
          : `Focus on well-being, ${name},! Set your health goals.`;
      case 7:
        return language === "nl"
          ? `Bijna klaar, ${name}! Laten we je ervaring aanpassen.`
          : `Almost there, ${name}! Let's customize your experience.`;
      default:
        return "";
    }
  };

  // Handle importing data
  const handleImportData = async () => {
    try {
      setIsImporting(true);
      // Provide haptic feedback
      if (!isWeb) {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
      }
      // Pick a document
      const result = await DocumentPicker.getDocumentAsync({
        type: ["application/json"],
        copyToCacheDirectory: true,
      });
      if (result.canceled) {
        setIsImporting(false);
        return;
      }
      try {
        // Read the file
        let fileContent;
        if (isWeb) {
          // For web, we need to handle the file differently
          try {
            const file = result.assets[0];
            if (!file) {
              throw new Error("No file selected");
            }
            const fileData = await new Promise<string>((resolve, reject) => {
              const reader = new FileReader();
              reader.onload = (e) => {
                try {
                  const content = e.target?.result;
                  if (typeof content === "string") {
                    resolve(content);
                  } else {
                    reject(new Error("Failed to read file content as string"));
                  }
                } catch (_error) {
                  reject(_error);
                }
              };
              reader.onerror = (e) => {
                reject(new Error("Error reading file: " + e.target?.error));
              };
              if (file instanceof Blob) {
                reader.readAsText(file);
              } else if (file.uri) {
                fetch(file.uri)
                  .then((response) => response.blob())
                  .then((blob) => reader.readAsText(blob))
                  .catch((_error) => reject(_error));
              } else {
                reject(new Error("Invalid file format"));
              }
            });
            const data = JSON.parse(fileData);
            await importUserData(data);
            if (!isWeb) {
              Haptics.notificationAsync(
                Haptics.NotificationFeedbackType.Success
              );
            }
            Alert.alert(
              "Success",
              t('onboarding.welcome.importSuccess'),
              [
                {
                  text: "OK",
                  onPress: () => {
                    router.replace("/(tabs)/dashboard");
                  },
                },
              ]
            );
          } catch (_error) {
            console.error("Error processing file on web:", _error);
            if (!isWeb) {
              Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
            }
            Alert.alert("Error", t('onboarding.welcome.invalidFile'));
            setIsImporting(false);
          }
        } else {
          fileContent = await FileSystem.readAsStringAsync(
            result.assets[0].uri
          );
          const data = JSON.parse(fileContent);
          await importUserData(data);
          if (!isWeb) {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
          }
          Alert.alert("Success", t('onboarding.welcome.importSuccess'), [
            {
              text: "OK",
              onPress: () => {
                router.replace("/(tabs)/dashboard");
              },
            },
          ]);
          setIsImporting(false);
        }
      } catch (_error) {
        console.error("Error importing data:", _error);
        if (!isWeb) {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        }
        Alert.alert("Error", t('onboarding.welcome.importError'));
        setIsImporting(false);
      }
    } catch (_error) {
      console.error("Error picking document:", _error);
      if (!isWeb) {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }
      Alert.alert("Error", t('onboarding.welcome.importError'));
      setIsImporting(false);
    }
  };

  const renderStep = () => {
    const animatedStyle = {
      opacity: fadeAnim,
      transform: [{ translateY: slideAnim }, { scale: scaleAnim }],
    };
    switch (step) {
      case 0:
        return (
          <Animated.View style={animatedStyle}>
            <WelcomeStep
              isImporting={isImporting}
              onNext={handleNext}
              onImportData={handleImportData}
            />
          </Animated.View>
        );
      case 1:
        return (
          <Animated.View style={animatedStyle}>
            <LanguageStep
              language={language}
              onLanguageChange={setLanguage}
              onNext={handleNext}
              onBack={handleBack}
            />
          </Animated.View>
        );
      case 2:
        return (
          <Animated.View style={animatedStyle}>
            <NameStep
              name={name}
              onNameChange={setName}
              onNext={handleNext}
              onBack={handleBack}
              error={errors.name}
            />
          </Animated.View>
        );
      case 3:
        return (
          <Animated.View style={animatedStyle}>
            <AddictionStep
              addiction={addiction}
              customAddiction={customAddiction}
              onAddictionChange={setAddiction}
              onCustomAddictionChange={setCustomAddiction}
              onNext={handleNext}
              onBack={handleBack}
              errors={errors}
              personalizedMessage={getPersonalizedMessage()}
            />
          </Animated.View>
        );
      case 4:
        return (
          <Animated.View style={animatedStyle}>
            <RecoveryGoalStep
              recoveryGoalType={recoveryGoalType}
              onRecoveryGoalTypeChange={setRecoveryGoalType}
              onNext={handleNext}
              onBack={handleBack}
              personalizedMessage={getPersonalizedMessage()}
              error={errors.recoveryGoalType}
            />
          </Animated.View>
        );
      case 5:
        return (
          <Animated.View style={animatedStyle}>
            <SobrietyStep
              sobrietyDate={sobrietyDate}
              showDatePicker={showDatePicker}
              usageAmount={usageAmount}
              usageUnit={usageUnit}
              language={language}
              addiction={addiction}
              onDateChange={onDateChange}
              onShowDatePickerChange={setShowDatePicker}
              onUsageAmountChange={setUsageAmount}
              onUsageUnitChange={setUsageUnit}
              onNext={handleNext}
              onBack={handleBack}
              onSkip={handleSkip}
              personalizedMessage={getPersonalizedMessage()}
            />
          </Animated.View>
        );
      case 6:
        return (
          <Animated.View style={animatedStyle}>
            <HealthGoalsStep
              healthGoals={healthGoals}
              language={language}
              onHealthGoalChange={(goalId, goal) => {
                setHealthGoals((prev) => ({
                  ...prev,
                  [goalId]: goal,
                }));
              }}
              onNext={handleNext}
              onBack={handleBack}
              onSkip={handleSkip}
              personalizedMessage={getPersonalizedMessage()}
            />
          </Animated.View>
        );
      case 7:
        return (
          <Animated.View style={animatedStyle}>
            <CustomizeStep
              dashboardView={dashboardView}
              usageCost={usageCost}
              costFrequency={costFrequency}
              onDashboardViewChange={setDashboardView}
              onUsageCostChange={setUsageCost}
              onCostFrequencyChange={setCostFrequency}
              onNext={handleNext}
              onBack={handleBack}
              onSkip={handleSkip}
              personalizedMessage={getPersonalizedMessage()}
            />
          </Animated.View>
        );
      default:
        return null;
    }
  };

  return (
    <SafeAreaView
      style={[styles.container, { backgroundColor: colors.background }]}
    >
      <StatusBar style={currentTheme === "dark" ? "light" : "dark"} />
      <ScrollView
        ref={scrollViewRef}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        keyboardShouldPersistTaps="handled"
      >
        {renderStep()}
      </ScrollView>
      {step > 0 && (
        <View
          style={[
            styles.progressContainer,
            { backgroundColor: colors.background },
          ]}
        >
          <View style={styles.progressBar}>
            {[1, 2, 3, 4, 5, 6, 7].map((i) => (
              <View
                key={i}
                style={[
                  styles.progressDot,
                  {
                    backgroundColor: i <= step ? colors.primary : colors.border,
                    transform: [{ scale: i === step ? 1.2 : 1 }],
                  },
                ]}
              />
            ))}
          </View>
          <Text style={[styles.progressText, { color: colors.textSecondary }]}>
            {step} of 7
          </Text>
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  progressBar: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 8,
  },
  progressContainer: {
    alignItems: "center",
    borderTopColor: "rgba(0,0,0,0.08)",
    borderTopWidth: 1,
    paddingHorizontal: 24,
    paddingVertical: 20,
  },
  progressDot: {
    borderRadius: 5,
    height: 10,
    marginHorizontal: 6,
    width: 10,
  },
  progressText: {
    fontSize: 13,
    fontWeight: "600",
    letterSpacing: 0.5,
  },
  scrollContent: {
    flexGrow: 1,
    minHeight: height - 200,
    padding: 24,
  },
});