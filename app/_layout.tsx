import React from "react";
import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect, useState } from "react";
import * as Notifications from 'expo-notifications';
import { ThemeProvider } from "@/context/theme-context";
import "../global.css"; // Import global CSS for NativeWind
import { ErrorBoundary } from "./error-boundary";
import { DailyCheckInModal } from "@/components/checkin/DailyCheckInModal";

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: "(tabs)",
};
// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();
export default function RootLayout() {
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });
  useEffect(() => {
    if (error) {
      console.error(error);
      throw error;
    }
  }, [error]);
  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);
  if (!loaded) {
    return null;
  }
  return (
    <ErrorBoundary>
      <ThemeProvider>
        <RootLayoutNav />
      </ThemeProvider>
    </ErrorBoundary>
  );
}
function RootLayoutNav() {
  const [showCheckInModal, setShowCheckInModal] = useState(false);

  useEffect(() => {
    // Handle notification responses
    const subscription = Notifications.addNotificationResponseReceivedListener(response => {
      const notificationData = response.notification.request.content.data;

      if (notificationData?.type === 'daily-checkin') {
        setShowCheckInModal(true);
      }
    });

    return () => subscription.remove();
  }, []);

  return (
    <>
      <Stack
        screenOptions={{
          headerBackTitle: "Back",
        }}
      >
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen name="modal" options={{ presentation: "modal" }} />
      </Stack>

      {/* Daily Check-in Modal */}
      <DailyCheckInModal
        visible={showCheckInModal}
        onClose={() => setShowCheckInModal(false)}
        onComplete={() => {
          setShowCheckInModal(false);
          // Could add success feedback here
        }}
      />
    </>
  );
}