import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  ScrollView,
  RefreshControl,
  TouchableOpacity,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Stack, useRouter } from 'expo-router';
import { BlurView } from 'expo-blur';
import { Menu } from 'lucide-react-native';
import { useUserStore } from '@/store/user/user-store';
import { useTheme } from '@/context/theme-context';
import { useQuotesStore, Quote } from '@/store/content/quotes-store';
import Colors from '@/constants/colors';

// Import new components
import { HeroSection } from '@/components/dashboard/hero/HeroSection';
import { TodaysSnapshot } from '@/components/dashboard/overview/TodaysSnapshot';
import { PrimaryActions } from '@/components/dashboard/actions/PrimaryActions';
import { MotivationSection } from '@/components/dashboard/motivation/MotivationSection';
import { DailyCheckInModal } from '@/components/checkin/DailyCheckInModal';

export default function DashboardScreen() {
  const { profile } = useUserStore();
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { quotes, fetchQuotes } = useQuotesStore();
  const router = useRouter();

  // State
  const [refreshing, setRefreshing] = useState(false);
  const [randomQuote, setRandomQuote] = useState<Quote | null>(null);
  const [isCheckInModalVisible, setIsCheckInModalVisible] = useState(false);

  // Calculate sobriety data
  const calculateSobrietyData = useCallback(() => {
    if (!profile?.sobrietyDate) {
      return { diffDays: 0, savedAmount: 0 };
    }
    
    const sobrietyDate = new Date(profile.sobrietyDate);
    const currentDate = new Date();
    const diffTime = currentDate.getTime() - sobrietyDate.getTime();
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    let savedAmount = 0;
    if (profile.usageCost && profile.usageCost > 0) {
      if (profile.costFrequency === 'daily') {
        savedAmount = profile.usageCost * diffDays;
      } else if (profile.costFrequency === 'weekly') {
        savedAmount = profile.usageCost * (diffDays / 7);
      } else if (profile.costFrequency === 'monthly') {
        savedAmount = profile.usageCost * (diffDays / 30);
      }
    }
    
    return { diffDays, savedAmount };
  }, [profile]);

  // Get next milestone
  const getNextMilestone = useCallback((days: number) => {
    const milestones = [
      { days: 1, name: profile?.language === 'nl' ? '1 Dag' : '1 Day' },
      { days: 7, name: profile?.language === 'nl' ? '1 Week' : '1 Week' },
      { days: 30, name: profile?.language === 'nl' ? '1 Maand' : '1 Month' },
      { days: 90, name: profile?.language === 'nl' ? '3 Maanden' : '3 Months' },
      { days: 180, name: profile?.language === 'nl' ? '6 Maanden' : '6 Months' },
      { days: 365, name: profile?.language === 'nl' ? '1 Jaar' : '1 Year' },
      { days: 730, name: profile?.language === 'nl' ? '2 Jaar' : '2 Years' },
      { days: 1095, name: profile?.language === 'nl' ? '3 Jaar' : '3 Years' },
    ];
    
    return milestones.find(milestone => days < milestone.days) || {
      days: milestones[milestones.length - 1].days + 365,
      name: profile?.language === 'nl' ? 'Volgende Mijlpaal' : 'Next Milestone',
    };
  }, [profile?.language]);

  // Generate today's snapshot data
  const generateTodaysSnapshot = useCallback(() => {
    const today = new Date().toISOString().split('T')[0];
    
    // Health summary
    const todaysHealthMetrics = profile?.healthMetrics?.filter(metric =>
      metric.date === today
    ) || [];
    
    const healthSummary = {
      completedMetrics: todaysHealthMetrics.length,
      totalMetrics: 4, // sleep, hydration, exercise, pills
      topMetric: todaysHealthMetrics[0] ? {
        type: todaysHealthMetrics[0].metric,
        value: Number(todaysHealthMetrics[0].value),
        unit: todaysHealthMetrics[0].unit || '',
        progress: 75, // Calculate based on goals
      } : undefined,
    };

    // Mood trend
    const recentMoods = profile?.moodEntries?.slice(-7) || [];
    const currentMood = recentMoods[recentMoods.length - 1]?.mood || 3;
    const weeklyAverage = recentMoods.length > 0 
      ? recentMoods.reduce((sum, entry) => sum + entry.mood, 0) / recentMoods.length
      : 3;
    
    const moodTrend = {
      current: currentMood,
      trend: (currentMood > weeklyAverage ? 'up' : currentMood < weeklyAverage ? 'down' : 'stable') as 'up' | 'down' | 'stable',
      weeklyAverage,
    };

    // Weekly progress
    const weekStart = new Date();
    weekStart.setDate(weekStart.getDate() - weekStart.getDay());
    const weeklyCheckIns = profile?.dailyCheckIns?.filter(checkIn => {
      const checkInDate = new Date(checkIn.date);
      return checkInDate >= weekStart;
    }) || [];

    const weeklyProgress = {
      checkInsCompleted: weeklyCheckIns.length,
      totalDays: 7,
      streak: calculateCurrentStreak(),
    };

    return { healthSummary, moodTrend, weeklyProgress };
  }, [profile]);

  // Calculate current streak
  const calculateCurrentStreak = useCallback(() => {
    if (!profile?.dailyCheckIns) return 0;
    
    const sortedCheckIns = [...profile.dailyCheckIns]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
    
    let streak = 0;
    const today = new Date();
    
    for (let i = 0; i < sortedCheckIns.length; i++) {
      const checkInDate = new Date(sortedCheckIns[i].date);
      const expectedDate = new Date(today);
      expectedDate.setDate(today.getDate() - i);
      
      if (checkInDate.toDateString() === expectedDate.toDateString()) {
        streak++;
      } else {
        break;
      }
    }
    
    return streak;
  }, [profile?.dailyCheckIns]);

  // Check if daily check-in is completed
  const isDailyCheckInCompleted = useCallback(() => {
    const today = new Date().toISOString().split('T')[0];
    return profile?.dailyCheckIns?.some(checkIn => checkIn.date === today) || false;
  }, [profile?.dailyCheckIns]);

  // Get random quote
  useEffect(() => {
    if (quotes.length > 0) {
      const randomIndex = Math.floor(Math.random() * quotes.length);
      setRandomQuote(quotes[randomIndex]);
    }
  }, [quotes]);

  // Refresh handler
  const onRefresh = async () => {
    setRefreshing(true);
    await fetchQuotes();
    if (quotes.length > 0) {
      const randomIndex = Math.floor(Math.random() * quotes.length);
      setRandomQuote(quotes[randomIndex]);
    }
    setRefreshing(false);
  };

  // Navigation handlers
  const handleCardPress = (cardType: string) => {
    switch (cardType) {
      case 'health':
        router.push('/(tabs)/progress');
        break;
      case 'mood':
        router.push('/(tabs)/progress');
        break;
      case 'progress':
        router.push('/(tabs)/progress');
        break;
    }
  };

  const handleCheckInPress = () => {
    setIsCheckInModalVisible(true);
  };

  const handleEmergencyPress = () => {
    router.push('/(tabs)/crisis');
  };

  const handleViewModeToggle = () => {
    // Toggle between cards and circular view
    // This would update user preferences
  };

  if (!profile) {
    return null;
  }

  const { diffDays, savedAmount } = calculateSobrietyData();
  const nextMilestone = getNextMilestone(diffDays);
  const milestoneProgress = Math.min((diffDays / nextMilestone.days) * 100, 100);
  const todaysSnapshot = generateTodaysSnapshot();

  const sobrietyData = {
    daysSober: diffDays,
    sobrietyDate: new Date(profile.sobrietyDate || new Date()),
    savedAmount,
    currency: profile.currency || 'USD',
  };

  const milestone = {
    current: 'Current milestone', // Calculate current milestone
    next: nextMilestone.name,
    progress: milestoneProgress,
    daysRemaining: nextMilestone.days - diffDays,
  };

  const dailyCheckIn = {
    completed: isDailyCheckInCompleted(),
    lastCompletedAt: profile.dailyCheckIns?.[profile.dailyCheckIns.length - 1]
      ? new Date(profile.dailyCheckIns[profile.dailyCheckIns.length - 1].completedAt)
      : undefined,
  };

  const emergency = {
    quickAccess: true,
    contactsAvailable: profile.emergencyContacts?.length || 0,
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]} edges={['top', 'bottom']}>
      <Stack.Screen
        options={{
          title: '',
          headerTransparent: true,
          headerBackground: () => (
            <BlurView intensity={80} tint={currentTheme} style={StyleSheet.absoluteFillObject} />
          ),
          headerLeft: () => (
            <TouchableOpacity style={styles.headerButton}>
              <Menu size={24} color={colors.text} />
            </TouchableOpacity>
          ),
        }}
      />

      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.contentContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} tintColor={colors.primary} />
        }
      >
        {/* Hero Section */}
        <HeroSection
          sobrietyData={sobrietyData}
          milestone={milestone}
          viewMode={profile.dashboardView || 'cards'}
          colors={colors}
          language={profile.language || 'en'}
          onViewModeToggle={handleViewModeToggle}
        />

        {/* Today's Snapshot */}
        <TodaysSnapshot
          healthSummary={todaysSnapshot.healthSummary}
          moodTrend={todaysSnapshot.moodTrend}
          weeklyProgress={todaysSnapshot.weeklyProgress}
          colors={colors}
          language={profile.language || 'en'}
          onCardPress={handleCardPress}
        />

        {/* Primary Actions */}
        <PrimaryActions
          dailyCheckIn={dailyCheckIn}
          emergency={emergency}
          colors={colors}
          language={profile.language || 'en'}
          onCheckInPress={handleCheckInPress}
          onEmergencyPress={handleEmergencyPress}
        />

        {/* Motivation Section */}
        <MotivationSection
          quote={randomQuote || undefined}
          colors={colors}
          language={profile.language || 'en'}
        />
      </ScrollView>

      {/* Daily Check-in Modal */}
      <DailyCheckInModal
        visible={isCheckInModalVisible}
        onClose={() => setIsCheckInModalVisible(false)}
        onComplete={() => {
          setIsCheckInModalVisible(false);
          // Refresh data after completion
        }}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    paddingBottom: 100,
  },
  headerButton: {
    padding: 8,
  },
});
