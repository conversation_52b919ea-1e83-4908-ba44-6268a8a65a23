import React from "react";
import { useState, useEffect, useCallback } from "react";
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Animated,
  Platform,
} from "react-native";
import {
  Trophy,
  Check,
  Clock,
  Calendar,
  Award,
  Star,
  Zap,
  Target,
  Heart,
  Shield,
  Flame,
  Sparkles,
} from "lucide-react-native";
import * as Haptics from "expo-haptics";
import { LinearGradient } from "expo-linear-gradient";
import { MilestoneType } from "@/types/milestone";
import { BaseTabProps } from "./types";

// Import the streak level system from SobrietyCard
const getStreakLevel = (days: number): { level: string; color: string; icon: React.ComponentType<{ size?: number; color?: string; strokeWidth?: number }> } => {
  if (days >= 365) return { level: 'legendary', color: '#FFD700', icon: Award };
  if (days >= 180) return { level: 'epic', color: '#9C27B0', icon: Trophy };
  if (days >= 90) return { level: 'hot', color: '#FF5722', icon: Flame };
  if (days >= 30) return { level: 'warm', color: '#FF9800', icon: Target };
  if (days >= 7) return { level: 'building', color: '#4CAF50', icon: Heart };
  return { level: 'starting', color: '#2196F3', icon: Calendar };
};
interface MilestonesTabProps extends BaseTabProps {
  fadeAnim: Animated.Value;
}
export const MilestonesTab: React.FC<MilestonesTabProps> = ({
  profile,
  colors,
  language,
  fadeAnim,
}) => {
  const [milestones, setMilestones] = useState<MilestoneType[]>([]);
  const [expandedMilestoneId, setExpandedMilestoneId] = useState<string | null>(
    null
  );
  const generateMilestones = useCallback(() => {
    if (!profile?.sobrietyDate) return;
    const sobrietyDate = new Date(profile.sobrietyDate);
    const today = new Date();
    // Calculate days sober
    const diffTime = Math.abs(today.getTime() - sobrietyDate.getTime());
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

    // Get current streak level for dynamic coloring
    const currentStreakLevel = getStreakLevel(diffDays);

    // Define enhanced milestone days with streak-based colors
    const milestoneDefinitions = [
      {
        id: "milestone_1",
        days: 1,
        name: profile.language === "nl" ? "Eerste Dag" : "First Day",
        icon: "calendar",
        color: getStreakLevel(1).color,
        category: "starting",
      },
      {
        id: "milestone_3",
        days: 3,
        name: profile.language === "nl" ? "Drie Dagen" : "Three Days",
        icon: "calendar",
        color: getStreakLevel(3).color,
        category: "starting",
      },
      {
        id: "milestone_7",
        days: 7,
        name: profile.language === "nl" ? "Eerste Week" : "One Week",
        icon: "heart",
        color: getStreakLevel(7).color,
        category: "building",
      },
      {
        id: "milestone_14",
        days: 14,
        name: profile.language === "nl" ? "Twee Weken" : "Two Weeks",
        icon: "heart",
        color: getStreakLevel(14).color,
        category: "building",
      },
      {
        id: "milestone_30",
        days: 30,
        name: profile.language === "nl" ? "Eerste Maand" : "One Month",
        icon: "target",
        color: getStreakLevel(30).color,
        category: "warm",
      },
      {
        id: "milestone_60",
        days: 60,
        name: profile.language === "nl" ? "Twee Maanden" : "Two Months",
        icon: "target",
        color: getStreakLevel(60).color,
        category: "warm",
      },
      {
        id: "milestone_90",
        days: 90,
        name: profile.language === "nl" ? "Drie Maanden" : "Three Months",
        icon: "flame",
        color: getStreakLevel(90).color,
        category: "hot",
      },
      {
        id: "milestone_120",
        days: 120,
        name: profile.language === "nl" ? "Vier Maanden" : "Four Months",
        icon: "flame",
        color: getStreakLevel(120).color,
        category: "hot",
      },
      {
        id: "milestone_180",
        days: 180,
        name: profile.language === "nl" ? "Zes Maanden" : "Six Months",
        icon: "trophy",
        color: getStreakLevel(180).color,
        category: "epic",
      },
      {
        id: "milestone_270",
        days: 270,
        name: profile.language === "nl" ? "Negen Maanden" : "Nine Months",
        icon: "trophy",
        color: getStreakLevel(270).color,
        category: "epic",
      },
      {
        id: "milestone_365",
        days: 365,
        name: profile.language === "nl" ? "Één Jaar" : "One Year",
        icon: "award",
        color: getStreakLevel(365).color,
        category: "legendary",
      },
      {
        id: "milestone_730",
        days: 730,
        name: profile.language === "nl" ? "Twee Jaar" : "Two Years",
        icon: "award",
        color: getStreakLevel(730).color,
        category: "legendary",
      },
      {
        id: "milestone_1095",
        days: 1095,
        name: profile.language === "nl" ? "Drie Jaar" : "Three Years",
        icon: "award",
        color: getStreakLevel(1095).color,
        category: "legendary",
      },
      {
        id: "milestone_1825",
        days: 1825,
        name: profile.language === "nl" ? "Vijf Jaar" : "Five Years",
        icon: "award",
        color: getStreakLevel(1825).color,
        category: "legendary",
      },
    ];
    // Calculate milestone status
    const calculatedMilestones = milestoneDefinitions.map((milestone) => {
      const achieved = diffDays >= milestone.days;
      const daysLeft = achieved ? 0 : milestone.days - diffDays;
      const progress = achieved ? 1 : diffDays / milestone.days;
      // Calculate milestone date
      const milestoneDate = new Date(sobrietyDate);
      milestoneDate.setDate(milestoneDate.getDate() + milestone.days);
      return {
        ...milestone,
        achieved,
        daysLeft,
        progress,
        date: milestoneDate,
      };
    });
    setMilestones(calculatedMilestones);
  }, [profile?.sobrietyDate, profile?.language, colors]);
  useEffect(() => {
    if (profile?.sobrietyDate) {
      generateMilestones();
    }
  }, [profile?.sobrietyDate, generateMilestones]);
  if (!profile) {
    return (
      <Animated.View style={[styles.tabContent, { opacity: fadeAnim }]}>
        <Text style={[styles.tabTitle, { color: colors.text }]}>
          {language === "nl" ? "Mijlpalen" : "Milestones"}
        </Text>
        <Text style={[styles.tabDescription, { color: colors.muted }]}>
          {language === "nl"
            ? "Geen profiel beschikbaar"
            : "No profile available"}
        </Text>
      </Animated.View>
    );
  }
  const toggleMilestoneExpand = (id: string) => {
    if (expandedMilestoneId === id) {
      setExpandedMilestoneId(null);
    } else {
      setExpandedMilestoneId(id);
      if (Platform.OS !== "web") {
        Haptics.selectionAsync();
      }
    }
  };
  const renderMilestoneIcon = (
    iconName: string,
    color: string,
    size: number = 24
  ) => {
    switch (iconName) {
      case "calendar":
        return <Calendar size={size} color={color} />;
      case "clock":
        return <Clock size={size} color={color} />;
      case "award":
        return <Award size={size} color={color} />;
      case "star":
        return <Star size={size} color={color} />;
      case "zap":
        return <Zap size={size} color={color} />;
      case "trophy":
        return <Trophy size={size} color={color} />;
      case "target":
        return <Target size={size} color={color} />;
      case "heart":
        return <Heart size={size} color={color} />;
      case "shield":
        return <Shield size={size} color={color} />;
      default:
        return <Trophy size={size} color={color} />;
    }
  };
  return (
    <Animated.View style={[styles.tabContent, { opacity: fadeAnim }]}>
      <Text style={[styles.tabTitle, { color: colors.text }]}>
        {profile.language === "nl" ? "Mijlpalen" : "Milestones"}
      </Text>
      <Text style={[styles.tabDescription, { color: colors.muted }]}>
        {profile.language === "nl"
          ? "Volg je voortgang en vier je successen"
          : "Track your progress and celebrate your successes"}
      </Text>
      <ScrollView style={styles.milestonesContainer}>
        {milestones.map((milestone) => (
          <TouchableOpacity
            key={milestone.id}
            style={[
              styles.milestoneCard,
              {
                backgroundColor: colors.card,
                borderColor: milestone.achieved
                  ? milestone.color
                  : colors.border,
              },
              milestone.achieved
                ? styles.milestoneCardAchieved
                : styles.milestoneCardDefault,
              milestone.achieved && milestone.days >= 365 && styles.legendaryMilestoneCard,
            ]}
            onPress={() => {
              toggleMilestoneExpand(milestone.id);
              if (milestone.achieved && Platform.OS !== "web") {
                Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
              }
            }}
            activeOpacity={0.7}
          >
            {/* Sparkle effect for legendary milestones */}
            {milestone.achieved && milestone.days >= 365 && (
              <View style={styles.sparkleEffect}>
                <Sparkles size={16} color="#FFD700" style={styles.sparkle1} />
                <Sparkles size={12} color="#FFF" style={styles.sparkle2} />
              </View>
            )}
            <View style={styles.milestoneHeader}>
              <View style={styles.milestoneIconContainer}>
                <View
                  style={[
                    styles.milestoneIcon,
                    {
                      backgroundColor: milestone.achieved
                        ? milestone.color + "20"
                        : colors.muted + "20",
                    },
                  ]}
                >
                  {renderMilestoneIcon(
                    milestone.icon,
                    milestone.achieved ? milestone.color : colors.muted
                  )}
                </View>
              </View>
              <View style={styles.milestoneInfo}>
                <View style={styles.milestoneNameContainer}>
                  <Text style={[styles.milestoneName, { color: colors.text }]}>
                    {milestone.name}
                  </Text>
                  {/* Streak level badge */}
                  <View style={[
                    styles.streakBadge,
                    { backgroundColor: getStreakLevel(milestone.days).color + '20' }
                  ]}>
                    <Text style={[
                      styles.streakBadgeText,
                      { color: getStreakLevel(milestone.days).color }
                    ]}>
                      {profile.language === "nl" ?
                        (getStreakLevel(milestone.days).level === 'legendary' ? 'Leg' :
                         getStreakLevel(milestone.days).level === 'epic' ? 'Epic' :
                         getStreakLevel(milestone.days).level === 'hot' ? 'Hot' :
                         getStreakLevel(milestone.days).level === 'warm' ? 'Warm' :
                         getStreakLevel(milestone.days).level === 'building' ? 'Build' : 'Start') :
                        getStreakLevel(milestone.days).level.charAt(0).toUpperCase() + getStreakLevel(milestone.days).level.slice(1, 3)}
                    </Text>
                  </View>
                </View>
                <Text style={[styles.milestoneDays, { color: colors.muted }]}>
                  {milestone.days}{" "}
                  {profile.language === "nl" ? "dagen" : "days"}
                </Text>
              </View>
              <View style={styles.milestoneStatus}>
                {milestone.achieved ? (
                  <View
                    style={[
                      styles.achievedBadge,
                      { backgroundColor: milestone.color },
                    ]}
                  >
                    <Check size={16} color="#fff" />
                  </View>
                ) : (
                  <Text style={[styles.daysLeftText, { color: colors.muted }]}>
                    {milestone.daysLeft}{" "}
                    {profile.language === "nl" ? "dagen te gaan" : "days left"}
                  </Text>
                )}
              </View>
            </View>
            {/* Progress bar */}
            <View
              style={[
                styles.progressBarContainer,
                { backgroundColor: colors.border + "40" },
              ]}
            >
              <View
                style={[
                  styles.progressBar,
                  {
                    width: `${milestone.progress * 100}%`,
                    backgroundColor: milestone.achieved
                      ? milestone.color
                      : colors.primary,
                  },
                ]}
              />
            </View>
            {expandedMilestoneId === milestone.id && (
              <View style={styles.milestoneDetails}>
                <View style={styles.milestoneDetailItem}>
                  <Text style={[styles.detailLabel, { color: colors.muted }]}>
                    {profile.language === "nl"
                      ? "Datum bereikt"
                      : "Date achieved"}
                    :
                  </Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>
                    {milestone.achieved
                      ? milestone.date.toLocaleDateString(
                          profile.language === "nl" ? "nl-NL" : "en-US"
                        )
                      : profile.language === "nl"
                      ? "Nog niet bereikt"
                      : "Not yet achieved"}
                  </Text>
                </View>
                <View style={styles.milestoneDetailItem}>
                  <Text style={[styles.detailLabel, { color: colors.muted }]}>
                    {profile.language === "nl" ? "Voortgang" : "Progress"}:
                  </Text>
                  <Text style={[styles.detailValue, { color: colors.text }]}>
                    {Math.round(milestone.progress * 100)}%
                  </Text>
                </View>
                {milestone.achieved && (
                  <LinearGradient
                    colors={[milestone.color + "20", milestone.color + "10"]}
                    style={styles.achievementBanner}
                    start={{ x: 0, y: 0 }}
                    end={{ x: 1, y: 0 }}
                  >
                    <Trophy size={16} color={milestone.color} />
                    <Text
                      style={[
                        styles.achievementText,
                        { color: milestone.color },
                      ]}
                    >
                      {profile.language === "nl"
                        ? "Mijlpaal bereikt!"
                        : "Milestone achieved!"}
                    </Text>
                  </LinearGradient>
                )}
              </View>
            )}
          </TouchableOpacity>
        ))}
      </ScrollView>
    </Animated.View>
  );
};
const styles = StyleSheet.create({


  achievedBadge: {
    alignItems: "center",
    borderRadius: 14,
    height: 28,
    justifyContent: "center",
    width: 28,
  },
  achievementBanner: {
    alignItems: "center",
    borderRadius: 8,
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 8,
    padding: 12,
  },
  achievementText: {
    fontSize: 14,
    fontWeight: "600",
    marginLeft: 8,
  },
  daysLeftText: {
    fontSize: 14,
  },
  detailLabel: {
    fontSize: 14,
  },
  detailValue: {
    fontSize: 14,
    fontWeight: "500",
  },
  milestoneCard: {
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
    padding: 16,
  },
  milestoneCardAchieved: {
    borderLeftWidth: 4,
  },
  milestoneCardDefault: {
    borderLeftWidth: 1,
  },
  milestoneDays: {
    fontSize: 14,
  },
  milestoneDetailItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  milestoneDetails: {
    borderTopColor: "#eaeaea",
    borderTopWidth: 1,
    marginTop: 12,
    paddingTop: 12,
  },
  milestoneHeader: {
    alignItems: "center",
    flexDirection: "row",
    marginBottom: 12,
  },
  milestoneIcon: {
    alignItems: "center",
    borderRadius: 24,
    height: 48,
    justifyContent: "center",
    width: 48,
  },
  milestoneIconContainer: {
    marginRight: 16,
  },
  milestoneInfo: {
    flex: 1,
  },
  milestoneName: {
    fontSize: 18,
    fontWeight: "600",
    marginBottom: 4,
  },
  milestoneStatus: {
    alignItems: "flex-end",
  },
  milestonesContainer: {
    flex: 1,
  },
  progressBar: {
    borderRadius: 4,
    height: "100%",
  },
  progressBarContainer: {
    borderRadius: 4,
    height: 8,
    marginBottom: 12,
    overflow: "hidden",
  },
  tabContent: {
    flex: 1,
    padding: 20,
  },
  tabDescription: {
    fontSize: 16,
    marginBottom: 24,
  },
  tabTitle: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 8,
  },
  milestoneNameContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    gap: 8,
    marginBottom: 4,
  },
  streakBadge: {
    alignItems: 'center',
    borderRadius: 8,
    justifyContent: 'center',
    paddingHorizontal: 6,
    paddingVertical: 2,
  },
  streakBadgeText: {
    fontSize: 10,
    fontWeight: '600',
    textTransform: 'capitalize',
  },
  legendaryMilestoneCard: {
    borderColor: '#FFD700',
    borderWidth: 2,
    shadowColor: '#FFD700',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  sparkleEffect: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 10,
  },
  sparkle1: {
    position: 'absolute',
    top: 0,
    right: 0,
  },
  sparkle2: {
    position: 'absolute',
    top: -4,
    right: 12,
  },
});