import React from 'react';
import { StyleSheet, Text, View, ScrollView } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { 
  TrendingUp, 
  TrendingDown, 
  BarChart3,
  Calendar,
  Target,
  Activity,
  Brain,
  Heart
} from 'lucide-react-native';
import { UserProfile } from '@/types/user';

interface Colors {
  primary: string;
  success: string;
  warning: string;
  info: string;
  danger: string;
  secondary: string;
  card: string;
  border: string;
  text: string;
  textSecondary: string;
  background: string;
}

interface DetailedInsightsProps {
  profile: UserProfile;
  colors: Colors;
  language: 'en' | 'nl';
}

interface DetailedInsight {
  id: string;
  title: string;
  value: string;
  subtitle: string;
  trend: 'up' | 'down' | 'stable';
  trendValue: number;
  icon: React.ComponentType<{ size: number; color: string }>;
  color: string;
  category: 'performance' | 'consistency' | 'improvement' | 'milestone';
}

export const DetailedInsights: React.FC<DetailedInsightsProps> = ({
  profile,
  colors,
  language,
}) => {
  // Calculate detailed insights from profile data
  const calculateDetailedInsights = (): DetailedInsight[] => {
    const insights: DetailedInsight[] = [];
    
    // Sobriety Performance
    if (profile.sobrietyDate) {
      const daysSober = Math.floor(
        (Date.now() - new Date(profile.sobrietyDate).getTime()) / (1000 * 60 * 60 * 24)
      );
      
      // Calculate longest streak vs current
      const longestStreak = daysSober; // Simplified - would calculate from relapse data
      const streakPerformance = (daysSober / Math.max(longestStreak, 1)) * 100;
      
      insights.push({
        id: 'sobriety_performance',
        title: language === 'nl' ? 'Nuchterheid Prestatie' : 'Sobriety Performance',
        value: `${daysSober} ${language === 'nl' ? 'dagen' : 'days'}`,
        subtitle: language === 'nl' ? 'Huidige reeks' : 'Current streak',
        trend: 'up',
        trendValue: streakPerformance,
        icon: Target,
        color: colors.success,
        category: 'performance',
      });
    }

    // Mood Stability Analysis
    if (profile.moodEntries && profile.moodEntries.length > 0) {
      const recentMoods = profile.moodEntries.slice(-30); // Last 30 entries
      const moodVariance = calculateMoodVariance(recentMoods);
      const averageMood = recentMoods.reduce((sum, entry) => sum + entry.mood, 0) / recentMoods.length;
      
      // Determine trend based on recent vs older entries
      const recentAvg = recentMoods.slice(-7).reduce((sum, entry) => sum + entry.mood, 0) / 7;
      const olderAvg = recentMoods.slice(0, 7).reduce((sum, entry) => sum + entry.mood, 0) / 7;
      const moodTrend = recentAvg > olderAvg ? 'up' : recentAvg < olderAvg ? 'down' : 'stable';
      
      insights.push({
        id: 'mood_stability',
        title: language === 'nl' ? 'Stemming Stabiliteit' : 'Mood Stability',
        value: averageMood.toFixed(1),
        subtitle: language === 'nl' ? 'Gemiddelde (30 dagen)' : 'Average (30 days)',
        trend: moodTrend,
        trendValue: Math.abs(recentAvg - olderAvg) * 20, // Convert to percentage
        icon: Brain,
        color: colors.primary,
        category: 'improvement',
      });
    }

    // Health Metrics Consistency
    if (profile.healthMetrics && profile.healthMetrics.length > 0) {
      const last30Days = getLast30Days();
      const healthDaysWithData = last30Days.filter(date => 
        profile.healthMetrics?.some(metric => metric.date === date)
      ).length;
      
      const consistencyPercentage = (healthDaysWithData / 30) * 100;
      const trend = consistencyPercentage >= 70 ? 'up' : consistencyPercentage >= 40 ? 'stable' : 'down';
      
      insights.push({
        id: 'health_consistency',
        title: language === 'nl' ? 'Gezondheid Consistentie' : 'Health Consistency',
        value: `${healthDaysWithData}/30`,
        subtitle: language === 'nl' ? 'Dagen met data' : 'Days with data',
        trend,
        trendValue: consistencyPercentage,
        icon: Heart,
        color: colors.info,
        category: 'consistency',
      });
    }

    // Check-in Frequency
    if (profile.dailyCheckIns && profile.dailyCheckIns.length > 0) {
      const last30Days = getLast30Days();
      const checkInDays = last30Days.filter(date => 
        profile.dailyCheckIns?.some(checkIn => checkIn.date === date)
      ).length;
      
      const checkInPercentage = (checkInDays / 30) * 100;
      const trend = checkInPercentage >= 80 ? 'up' : checkInPercentage >= 50 ? 'stable' : 'down';
      
      insights.push({
        id: 'checkin_frequency',
        title: language === 'nl' ? 'Check-in Frequentie' : 'Check-in Frequency',
        value: `${checkInDays}/30`,
        subtitle: language === 'nl' ? 'Dagen voltooid' : 'Days completed',
        trend,
        trendValue: checkInPercentage,
        icon: Calendar,
        color: colors.warning,
        category: 'consistency',
      });
    }

    // Weekly Activity Score
    const weeklyScore = calculateWeeklyActivityScore(profile);
    insights.push({
      id: 'weekly_activity',
      title: language === 'nl' ? 'Wekelijkse Activiteit' : 'Weekly Activity',
      value: `${weeklyScore}/100`,
      subtitle: language === 'nl' ? 'Activiteit score' : 'Activity score',
      trend: weeklyScore >= 70 ? 'up' : weeklyScore >= 40 ? 'stable' : 'down',
      trendValue: weeklyScore,
      icon: Activity,
      color: colors.secondary,
      category: 'performance',
    });

    return insights;
  };

  const calculateMoodVariance = (moods: any[]) => {
    if (moods.length === 0) return 0;
    const avg = moods.reduce((sum, entry) => sum + entry.mood, 0) / moods.length;
    const variance = moods.reduce((sum, entry) => sum + Math.pow(entry.mood - avg, 2), 0) / moods.length;
    return Math.sqrt(variance);
  };

  const getLast30Days = () => {
    const days = [];
    for (let i = 0; i < 30; i++) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      days.push(date.toISOString().split('T')[0]);
    }
    return days;
  };

  const calculateWeeklyActivityScore = (profile: UserProfile) => {
    const last7Days = getLast30Days().slice(0, 7);
    let score = 0;
    
    // Check-ins (40 points)
    const checkIns = last7Days.filter(date => 
      profile.dailyCheckIns?.some(checkIn => checkIn.date === date)
    ).length;
    score += (checkIns / 7) * 40;
    
    // Health metrics (30 points)
    const healthDays = last7Days.filter(date => 
      profile.healthMetrics?.some(metric => metric.date === date)
    ).length;
    score += (healthDays / 7) * 30;
    
    // Mood entries (20 points)
    const moodDays = last7Days.filter(date => 
      profile.moodEntries?.some(entry => entry.date.startsWith(date))
    ).length;
    score += (moodDays / 7) * 20;
    
    // Consistency bonus (10 points)
    if (checkIns >= 5 && healthDays >= 4 && moodDays >= 3) {
      score += 10;
    }
    
    return Math.round(score);
  };

  const renderTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={14} color={colors.success} />;
      case 'down':
        return <TrendingDown size={14} color={colors.danger} />;
      default:
        return <BarChart3 size={14} color={colors.info} />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'performance': return colors.success;
      case 'consistency': return colors.warning;
      case 'improvement': return colors.primary;
      case 'milestone': return colors.secondary;
      default: return colors.info;
    }
  };

  const insights = calculateDetailedInsights();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === 'nl' ? 'Gedetailleerde Inzichten' : 'Detailed Insights'}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {language === 'nl' ? 'Diepgaande analyse van je voortgang' : 'In-depth analysis of your progress'}
        </Text>
      </View>

      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.insightsContainer}
      >
        {insights.map((insight) => (
          <View
            key={insight.id}
            style={[
              styles.insightCard,
              { backgroundColor: colors.card, borderColor: colors.border }
            ]}
          >
            <LinearGradient
              colors={[insight.color + '10', insight.color + '05']}
              style={styles.cardGradient}
            >
              <View style={styles.cardHeader}>
                <View style={[styles.iconContainer, { backgroundColor: insight.color + '20' }]}>
                  <insight.icon size={20} color={insight.color} />
                </View>
                <View style={styles.trendContainer}>
                  {renderTrendIcon(insight.trend)}
                  <Text style={[styles.trendValue, { color: colors.textSecondary }]}>
                    {insight.trendValue.toFixed(0)}%
                  </Text>
                </View>
              </View>

              <Text style={[styles.insightTitle, { color: colors.text }]}>
                {insight.title}
              </Text>

              <Text style={[styles.insightValue, { color: insight.color }]}>
                {insight.value}
              </Text>

              <Text style={[styles.insightSubtitle, { color: colors.textSecondary }]}>
                {insight.subtitle}
              </Text>

              <View style={styles.progressContainer}>
                <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        backgroundColor: insight.color,
                        width: `${Math.min(insight.trendValue, 100)}%`,
                      }
                    ]}
                  />
                </View>
              </View>
            </LinearGradient>
          </View>
        ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  header: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
  },
  subtitle: {
    fontSize: 14,
    lineHeight: 20,
    marginTop: 4,
  },
  insightsContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  insightCard: {
    borderRadius: 16,
    borderWidth: 1,
    elevation: 2,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    width: 200,
  },
  cardGradient: {
    padding: 16,
  },
  cardHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  iconContainer: {
    alignItems: 'center',
    borderRadius: 10,
    height: 36,
    justifyContent: 'center',
    width: 36,
  },
  trendContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  trendValue: {
    fontSize: 12,
    fontWeight: '600',
    marginLeft: 4,
  },
  insightTitle: {
    fontSize: 14,
    fontWeight: '600',
    marginBottom: 8,
  },
  insightValue: {
    fontSize: 24,
    fontWeight: '800',
    marginBottom: 4,
  },
  insightSubtitle: {
    fontSize: 12,
    marginBottom: 12,
  },
  progressContainer: {
    marginTop: 4,
  },
  progressBar: {
    borderRadius: 2,
    height: 4,
    overflow: 'hidden',
  },
  progressFill: {
    borderRadius: 2,
    height: 4,
  },
});
