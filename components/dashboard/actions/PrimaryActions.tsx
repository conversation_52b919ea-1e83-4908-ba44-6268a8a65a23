import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Platform,
} from 'react-native';
import { 
  Heart, 
  Shield, 
  CheckCircle,
  Clock,
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';


interface DailyCheckInAction {
  completed: boolean;
  lastCompletedAt?: Date;
}

interface EmergencyAction {
  quickAccess: boolean;
  contactsAvailable: number;
}

interface ColorScheme {
  primary: string;
  danger: string;
  success: string;
  card: string;
  border: string;
  text: string;
  textSecondary: string;
  background: string;
}

interface PrimaryActionsProps {
  dailyCheckIn: DailyCheckInAction;
  emergency: EmergencyAction;
  colors: ColorScheme;
  language: 'en' | 'nl';
  onCheckInPress: () => void;
  onEmergencyPress: () => void;
}

export const PrimaryActions: React.FC<PrimaryActionsProps> = ({
  dailyCheckIn,
  emergency,
  colors,
  language,
  onCheckInPress,
  onEmergencyPress,
}) => {

  const handlePress = (action: 'checkIn' | 'emergency') => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    
    if (action === 'checkIn') {
      onCheckInPress();
    } else {
      onEmergencyPress();
    }
  };

  const getCheckInStatus = () => {
    if (dailyCheckIn.completed) {
      return {
        icon: CheckCircle,
        color: colors.success,
        title: language === 'nl' ? 'Check-in Voltooid' : 'Check-in Completed',
        subtitle: language === 'nl' ? 'Je dagelijkse check-in is voltooid' : 'Your daily check-in is completed',
        buttonText: language === 'nl' ? 'Bekijk Check-in' : 'View Check-in',
      };
    }

    return {
      icon: Heart,
      color: colors.primary,
      title: language === 'nl' ? 'Dagelijkse Check-in' : 'Daily Check-in',
      subtitle: language === 'nl' ? 'Hoe voel je je vandaag?' : 'How are you feeling today?',
      buttonText: language === 'nl' ? 'Start Check-in' : 'Start Check-in',
    };
  };

  const formatLastCompleted = (date: Date) => {
    const now = new Date();
    const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffHours < 1) {
      return language === 'nl' ? 'zojuist' : 'just now';
    } else if (diffHours < 24) {
      return language === 'nl' ? `${diffHours} uur geleden` : `${diffHours} hours ago`;
    } else {
      return date.toLocaleDateString(
        language === 'nl' ? 'nl-NL' : 'en-US',
        { month: 'short', day: 'numeric' }
      );
    }
  };

  const checkInStatus = getCheckInStatus();

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === 'nl' ? 'Snelle Acties' : 'Quick Actions'}
        </Text>
        <Text style={[styles.subtitle, { color: colors.textSecondary }]}>
          {language === 'nl' ? 'Essentiële dagelijkse taken' : 'Essential daily tasks'}
        </Text>
      </View>

      <View style={styles.actionsContainer}>
        {/* Daily Check-in Action */}
        <TouchableOpacity
          style={[
            styles.actionCard,
            { backgroundColor: colors.card, borderColor: colors.border },
            dailyCheckIn.completed && styles.completedCard,
          ]}
          onPress={() => handlePress('checkIn')}
          accessibilityRole="button"
          accessibilityLabel={`${checkInStatus.title}. ${checkInStatus.subtitle}`}
          accessibilityState={{ selected: dailyCheckIn.completed }}
        >
          <LinearGradient
            colors={[
              checkInStatus.color + '10',
              checkInStatus.color + '05',
            ]}
            style={styles.cardGradient}
          >
            <View style={styles.cardHeader}>
              <View style={[
                styles.actionIcon,
                { backgroundColor: checkInStatus.color + '20' }
              ]}>
                <checkInStatus.icon size={24} color={checkInStatus.color} />
              </View>
              
              {dailyCheckIn.completed && dailyCheckIn.lastCompletedAt && (
                <View style={styles.timeContainer}>
                  <Clock size={14} color={colors.textSecondary} />
                  <Text style={[styles.timeText, { color: colors.textSecondary }]}>
                    {formatLastCompleted(dailyCheckIn.lastCompletedAt)}
                  </Text>
                </View>
              )}
            </View>

            <Text style={[styles.actionTitle, { color: colors.text }]}>
              {checkInStatus.title}
            </Text>
            
            <Text style={[styles.actionSubtitle, { color: colors.textSecondary }]}>
              {checkInStatus.subtitle}
            </Text>

            <View style={[
              styles.actionButton,
              { 
                backgroundColor: dailyCheckIn.completed 
                  ? colors.success + '20' 
                  : checkInStatus.color + '20' 
              }
            ]}>
              <Text style={[
                styles.actionButtonText,
                { 
                  color: dailyCheckIn.completed 
                    ? colors.success 
                    : checkInStatus.color 
                }
              ]}>
                {checkInStatus.buttonText}
              </Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>

        {/* Emergency Access Action */}
        <TouchableOpacity
          style={[
            styles.actionCard,
            styles.emergencyCard,
            { backgroundColor: colors.card, borderColor: colors.danger + '30' },
          ]}
          onPress={() => handlePress('emergency')}
          accessibilityRole="button"
          accessibilityLabel={`${language === 'nl' ? 'Noodhulp' : 'Emergency'}. ${language === 'nl' ? 'Krijg onmiddellijke hulp' : 'Get immediate help'}`}
        >
          <LinearGradient
            colors={[colors.danger + '10', colors.danger + '05']}
            style={styles.cardGradient}
          >
            <View style={styles.cardHeader}>
              <View style={[
                styles.actionIcon,
                { backgroundColor: colors.danger + '20' }
              ]}>
                <Shield size={24} color={colors.danger} />
              </View>
              
              {emergency.contactsAvailable > 0 && (
                <View style={styles.contactsContainer}>
                  <Text style={[styles.contactsText, { color: colors.textSecondary }]}>
                    {emergency.contactsAvailable} {language === 'nl' ? 'contacten beschikbaar' : 'contacts available'}
                  </Text>
                </View>
              )}
            </View>

            <Text style={[styles.actionTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Noodhulp' : 'Emergency'}
            </Text>

            <Text style={[styles.actionSubtitle, { color: colors.textSecondary }]}>
              {language === 'nl' ? 'Krijg onmiddellijke hulp wanneer je het nodig hebt' : 'Get immediate help when you need it'}
            </Text>

            <View style={[
              styles.actionButton,
              { backgroundColor: colors.danger + '20' }
            ]}>
              <Text style={[
                styles.actionButtonText,
                { color: colors.danger }
              ]}>
                {language === 'nl' ? 'Krijg Hulp' : 'Get Help'}
              </Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
    paddingHorizontal: 20,
  },
  header: {
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
  },
  subtitle: {
    fontSize: 15,
    lineHeight: 20,
    marginTop: 4,
  },
  actionsContainer: {
    gap: 16,
  },
  actionCard: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 3,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  completedCard: {
    opacity: 0.8,
  },
  emergencyCard: {
    borderWidth: 2,
  },
  cardGradient: {
    padding: 20,
  },
  cardHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  actionIcon: {
    alignItems: 'center',
    borderRadius: 16,
    height: 56,
    justifyContent: 'center',
    width: 56,
  },
  timeContainer: {
    alignItems: 'center',
    flexDirection: 'row',
  },
  timeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  contactsContainer: {
    alignItems: 'center',
  },
  contactsText: {
    fontSize: 12,
    fontWeight: '500',
  },
  actionTitle: {
    fontSize: 20,
    fontWeight: '700',
    marginBottom: 8,
  },
  actionSubtitle: {
    fontSize: 15,
    lineHeight: 20,
    marginBottom: 20,
  },
  actionButton: {
    alignItems: 'center',
    borderRadius: 12,
    justifyContent: 'center',
    paddingVertical: 12,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
});
