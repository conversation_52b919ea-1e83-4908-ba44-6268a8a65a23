import React, { useRef, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Animated,
  AccessibilityInfo,
} from 'react-native';
import { Target } from 'lucide-react-native';

interface ColorScheme {
  primary: string;
  text: string;
  textSecondary: string;
}

interface SobrietyCounterProps {
  daysSober: number;
  sobrietyDate: Date;
  colors: ColorScheme;
  language: 'en' | 'nl';
}

export const SobrietyCounter: React.FC<SobrietyCounterProps> = ({
  daysSober,
  sobrietyDate,
  colors,
  language,
}) => {
  const countAnim = useRef(new Animated.Value(0)).current;
  const [animatedDays, setAnimatedDays] = useState(0);
  const [isReducedMotion, setIsReducedMotion] = useState(false);

  // Check for reduced motion preference
  useEffect(() => {
    const checkReducedMotion = async () => {
      const reducedMotion = await AccessibilityInfo.isReduceMotionEnabled();
      setIsReducedMotion(reducedMotion);
    };
    checkReducedMotion();
  }, []);

  // Animate the counter
  useEffect(() => {
    if (isReducedMotion) {
      setAnimatedDays(daysSober);
      return;
    }

    // Reset animation value
    countAnim.setValue(0);
    
    // Create listener for animated value
    const listener = countAnim.addListener(({ value }) => {
      setAnimatedDays(Math.floor(value));
    });

    // Start animation
    Animated.timing(countAnim, {
      toValue: daysSober,
      duration: Math.min(2000, daysSober * 20), // Max 2 seconds
      useNativeDriver: false, // Can't use native driver for value updates
    }).start();

    return () => {
      countAnim.removeListener(listener);
    };
  }, [daysSober, countAnim, isReducedMotion]);

  const formatDate = (date: Date) => {
    return date.toLocaleDateString(
      language === 'nl' ? 'nl-NL' : 'en-US',
      { 
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }
    );
  };

  return (
    <View 
      style={styles.container}
      accessibilityRole="text"
      accessibilityLabel={`${daysSober} ${language === 'nl' ? 'dagen nuchter' : 'days sober'}. ${language === 'nl' ? 'Nuchterheid gestart op' : 'Sobriety started on'} ${formatDate(sobrietyDate)}`}
    >
      <View style={styles.mainStat}>
        <View style={styles.iconContainer}>
          <Target size={28} color="#fff" />
        </View>
        <View style={styles.content}>
          <Text style={styles.number} accessibilityElementsHidden={true}>
            {animatedDays}
          </Text>
          <Text style={styles.label} accessibilityElementsHidden={true}>
            {language === 'nl' ? 'dagen nuchter' : 'days sober'}
          </Text>
        </View>
      </View>
      
      <View style={styles.startDate}>
        <Text style={styles.startDateLabel}>
          {language === 'nl' ? 'sinds' : 'since'}
        </Text>
        <Text style={styles.startDateValue}>
          {formatDate(sobrietyDate)}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  mainStat: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 20,
    flexDirection: 'row',
    padding: 20,
  },
  iconContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 16,
    height: 56,
    justifyContent: 'center',
    width: 56,
  },
  content: {
    flex: 1,
    marginLeft: 16,
  },
  number: {
    color: '#fff',
    fontSize: 42,
    fontWeight: '800',
    lineHeight: 48,
  },
  label: {
    color: 'rgba(255, 255, 255, 0.9)',
    fontSize: 16,
    fontWeight: '500',
    marginTop: 4,
  },
  startDate: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 12,
  },
  startDateLabel: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 14,
    fontWeight: '500',
  },
  startDateValue: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    marginLeft: 8,
  },
});
