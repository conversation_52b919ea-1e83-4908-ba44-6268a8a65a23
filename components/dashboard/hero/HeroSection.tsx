import React, { useRef, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Animated,
  Pressable,
  AccessibilityInfo,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Target, TrendingUp } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';
import { CircularView } from '@/components/dashboard/CircularView';
import { SobrietyCounter } from './SobrietyCounter';
import { MilestoneProgress } from './MilestoneProgress';
import { SavingsDisplay } from './SavingsDisplay';

interface SobrietyData {
  daysSober: number;
  sobrietyDate: Date;
  savedAmount: number;
  currency: 'USD' | 'EUR';
}

interface Milestone {
  current: string;
  next: string;
  progress: number;
  daysRemaining: number;
}

interface ColorScheme {
  primary: string;
  primaryDark: string;
  background: string;
  text: string;
  textSecondary: string;
  success: string;
  warning: string;
}

interface HeroSectionProps {
  sobrietyData: SobrietyData;
  milestone: Milestone;
  viewMode: 'cards' | 'circular';
  colors: ColorScheme;
  language: 'en' | 'nl';
  onViewModeToggle?: () => void;
}

export const HeroSection: React.FC<HeroSectionProps> = ({
  sobrietyData,
  milestone,
  viewMode,
  colors,
  language,
  onViewModeToggle,
}) => {
  const { t } = useTranslation();
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.95)).current;
  const [isReducedMotion, setIsReducedMotion] = useState(false);

  // Check for reduced motion preference
  useEffect(() => {
    const checkReducedMotion = async () => {
      const reducedMotion = await AccessibilityInfo.isReduceMotionEnabled();
      setIsReducedMotion(reducedMotion);
    };
    checkReducedMotion();
  }, []);

  // Entrance animation
  useEffect(() => {
    if (isReducedMotion) {
      // Skip animations for users who prefer reduced motion
      fadeAnim.setValue(1);
      scaleAnim.setValue(1);
      return;
    }

    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.spring(scaleAnim, {
        toValue: 1,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, scaleAnim, isReducedMotion]);

  // Get time-based greeting
  const getTimeBasedGreeting = () => {
    const hour = new Date().getHours();
    
    if (hour < 12) {
      return t('dashboard.greetings.morning');
    } else if (hour < 17) {
      return t('dashboard.greetings.afternoon');
    } else {
      return t('dashboard.greetings.evening');
    }
  };

  const greeting = getTimeBasedGreeting();
  const currentTime = new Date().toLocaleTimeString(
    language === 'nl' ? 'nl-NL' : 'en-US',
    { hour: '2-digit', minute: '2-digit' }
  );

  if (viewMode === 'circular') {
    return (
      <CircularView
        diffDays={sobrietyData.daysSober}
        sobrietyDate={sobrietyData.sobrietyDate}
        nextMilestone={milestone}
        savedAmount={sobrietyData.savedAmount}
        usageCost={0} // Will be passed from parent if needed
        _costFrequency="daily"
        language={language}
        currency={sobrietyData.currency}
        colors={colors}
        usageAmount=""
        _substanceType=""
      />
    );
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ scale: scaleAnim }],
        },
      ]}
      accessibilityRole="region"
      accessibilityLabel={t('dashboard.hero.accessibilityLabel')}
    >
      <LinearGradient
        colors={[colors.primary, colors.primaryDark, colors.primary + '90']}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
        style={styles.gradient}
      >
        {/* Background Pattern */}
        <View style={styles.backgroundPattern}>
          <View style={[styles.patternCircle, styles.patternCircle1]} />
          <View style={[styles.patternCircle, styles.patternCircle2]} />
          <View style={[styles.patternCircle, styles.patternCircle3]} />
        </View>

        <View style={styles.content}>
          {/* Greeting Section */}
          <View style={styles.greetingSection}>
            <View style={styles.greetingHeader}>
              <Text style={styles.greeting} accessibilityRole="text">
                {greeting}
              </Text>
              <Text style={styles.time} accessibilityLabel={`Current time: ${currentTime}`}>
                {currentTime}
              </Text>
            </View>
          </View>

          {/* Main Stats */}
          <View style={styles.statsContainer}>
            <SobrietyCounter
              daysSober={sobrietyData.daysSober}
              sobrietyDate={sobrietyData.sobrietyDate}
              colors={colors}
              language={language}
            />
            
            <View style={styles.secondaryStats}>
              <SavingsDisplay
                savedAmount={sobrietyData.savedAmount}
                currency={sobrietyData.currency}
                colors={colors}
                language={language}
              />
              
              <View style={styles.progressStat}>
                <View style={styles.progressIcon}>
                  <TrendingUp size={20} color="#fff" />
                </View>
                <View style={styles.progressContent}>
                  <Text style={styles.progressNumber}>
                    {milestone.progress.toFixed(0)}%
                  </Text>
                  <Text style={styles.progressLabel}>
                    {t('dashboard.hero.toGoal')}
                  </Text>
                </View>
              </View>
            </View>
          </View>

          {/* Milestone Progress */}
          <MilestoneProgress
            milestone={milestone}
            colors={colors}
            language={language}
          />
        </View>

        {/* View Mode Toggle */}
        {onViewModeToggle && (
          <Pressable
            style={styles.viewToggle}
            onPress={onViewModeToggle}
            accessibilityRole="button"
            accessibilityLabel={t('dashboard.hero.toggleView')}
            accessibilityHint={t('dashboard.hero.toggleViewHint')}
          >
            <Target size={16} color="rgba(255, 255, 255, 0.8)" />
          </Pressable>
        )}
      </LinearGradient>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 20,
    marginTop: 60,
    marginBottom: 24,
  },
  gradient: {
    borderRadius: 28,
    overflow: 'hidden',
    position: 'relative',
  },
  backgroundPattern: {
    ...StyleSheet.absoluteFillObject,
    zIndex: 1,
  },
  patternCircle: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 50,
    position: 'absolute',
  },
  patternCircle1: {
    height: 100,
    right: -20,
    top: -30,
    width: 100,
  },
  patternCircle2: {
    bottom: -40,
    height: 80,
    left: -10,
    width: 80,
  },
  patternCircle3: {
    height: 60,
    right: 30,
    top: 120,
    width: 60,
  },
  content: {
    padding: 28,
    position: 'relative',
    zIndex: 2,
  },
  greetingSection: {
    marginBottom: 28,
  },
  greetingHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  greeting: {
    color: 'rgba(255, 255, 255, 0.85)',
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
  },
  time: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  statsContainer: {
    marginBottom: 24,
  },
  secondaryStats: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 16,
  },
  progressStat: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: 4,
    padding: 16,
  },
  progressIcon: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    width: 40,
  },
  progressContent: {
    flex: 1,
    marginLeft: 12,
  },
  progressNumber: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
  },
  progressLabel: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
  viewToggle: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 20,
    height: 40,
    justifyContent: 'center',
    position: 'absolute',
    right: 16,
    top: 16,
    width: 40,
    zIndex: 3,
  },
  placeholder: {
    fontSize: 18,
    fontWeight: '600',
    padding: 40,
    textAlign: 'center',
  },
});
