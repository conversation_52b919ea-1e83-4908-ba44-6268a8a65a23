import React, { useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Animated,
} from 'react-native';
import { Target } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';

interface Milestone {
  current: string;
  next: string;
  progress: number;
  daysRemaining: number;
}

interface ColorScheme {
  primary: string;
  text: string;
  textSecondary: string;
}

interface MilestoneProgressProps {
  milestone: Milestone;
  colors: ColorScheme;
  language: 'en' | 'nl';
}

export const MilestoneProgress: React.FC<MilestoneProgressProps> = ({
  milestone,
  colors,
  language,
}) => {
  const { t } = useTranslation();
  const progressAnim = useRef(new Animated.Value(0)).current;
  const fadeAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    // Animate progress bar
    Animated.timing(progressAnim, {
      toValue: milestone.progress,
      duration: 1500,
      useNativeDriver: false,
    }).start();

    // Fade in animation
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, [milestone.progress, progressAnim, fadeAnim]);

  const progressWidth = progressAnim.interpolate({
    inputRange: [0, 100],
    outputRange: ['0%', '100%'],
    extrapolate: 'clamp',
  });

  return (
    <Animated.View 
      style={[styles.container, { opacity: fadeAnim }]}
      accessibilityRole="progressbar"
      accessibilityValue={{
        min: 0,
        max: 100,
        now: milestone.progress,
        text: `${milestone.progress.toFixed(0)}% ${t('dashboard.hero.progressToward')} ${milestone.next}`
      }}
    >
      <View style={styles.header}>
        <View style={styles.iconContainer}>
          <Target size={18} color="#fff" />
        </View>
        <View style={styles.textContainer}>
          <Text style={styles.title}>
            {t('dashboard.hero.nextMilestone')}
          </Text>
          <Text style={styles.subtitle}>
            {milestone.next}
          </Text>
        </View>
        <View style={styles.percentageContainer}>
          <Text style={styles.percentageText}>
            {milestone.progress.toFixed(0)}%
          </Text>
        </View>
      </View>
      
      <View style={styles.progressBarContainer}>
        <View style={styles.progressBarBackground} />
        <Animated.View
          style={[
            styles.progressBarFill,
            { width: progressWidth },
          ]}
        />
        <View style={styles.progressBarGlow} />
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.daysRemaining}>
          {milestone.daysRemaining} {t('dashboard.hero.daysToGo')}
        </Text>
      </View>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
  },
  header: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 12,
  },
  iconContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    height: 36,
    justifyContent: 'center',
    width: 36,
  },
  textContainer: {
    flex: 1,
    marginLeft: 12,
  },
  title: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    fontWeight: '500',
  },
  subtitle: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
  },
  percentageContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 12,
    justifyContent: 'center',
    minWidth: 50,
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  percentageText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '700',
  },
  progressBarContainer: {
    height: 8,
    position: 'relative',
  },
  progressBarBackground: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 4,
    height: 8,
    position: 'absolute',
    width: '100%',
  },
  progressBarFill: {
    backgroundColor: '#fff',
    borderRadius: 4,
    height: 8,
    shadowColor: '#fff',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.5,
    shadowRadius: 4,
  },
  progressBarGlow: {
    backgroundColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: 4,
    height: 8,
    position: 'absolute',
    width: '100%',
  },
  footer: {
    marginTop: 8,
  },
  daysRemaining: {
    color: 'rgba(255, 255, 255, 0.7)',
    fontSize: 12,
    fontWeight: '500',
    textAlign: 'center',
  },
});
