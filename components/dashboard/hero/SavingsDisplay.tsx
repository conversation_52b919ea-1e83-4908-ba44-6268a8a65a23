import React, { useRef, useEffect, useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Animated,
  AccessibilityInfo,
} from 'react-native';
import { DollarSign } from 'lucide-react-native';
import { useTranslation } from '@/hooks/useTranslation';

interface ColorScheme {
  success: string;
  text: string;
  textSecondary: string;
}

interface SavingsDisplayProps {
  savedAmount: number;
  currency: 'USD' | 'EUR';
  colors: ColorScheme;
  language: 'en' | 'nl';
}

export const SavingsDisplay: React.FC<SavingsDisplayProps> = ({
  savedAmount,
  currency,
  colors,
  language,
}) => {
  const { t } = useTranslation();
  const countAnim = useRef(new Animated.Value(0)).current;
  const [animatedAmount, setAnimatedAmount] = useState(0);
  const [isReducedMotion, setIsReducedMotion] = useState(false);

  // Check for reduced motion preference
  useEffect(() => {
    const checkReducedMotion = async () => {
      const reducedMotion = await AccessibilityInfo.isReduceMotionEnabled();
      setIsReducedMotion(reducedMotion);
    };
    checkReducedMotion();
  }, []);

  // Animate the savings counter
  useEffect(() => {
    if (isReducedMotion) {
      setAnimatedAmount(savedAmount);
      return;
    }

    // Reset animation value
    countAnim.setValue(0);
    
    // Create listener for animated value
    const listener = countAnim.addListener(({ value }) => {
      setAnimatedAmount(value);
    });

    // Start animation
    Animated.timing(countAnim, {
      toValue: savedAmount,
      duration: Math.min(2000, savedAmount * 2), // Max 2 seconds
      useNativeDriver: false,
    }).start();

    return () => {
      countAnim.removeListener(listener);
    };
  }, [savedAmount, countAnim, isReducedMotion]);

  const formatCurrency = (amount: number) => {
    const currencySymbol = currency === 'EUR' ? '€' : '$';
    return `${currencySymbol}${amount.toFixed(2)}`;
  };

  // Don't render if no savings to show
  if (savedAmount <= 0) {
    return null;
  }

  return (
    <View 
      style={styles.container}
      accessibilityRole="text"
      accessibilityLabel={`${t('dashboard.hero.saved')} ${formatCurrency(savedAmount)}`}
    >
      <View style={styles.iconContainer}>
        <DollarSign size={20} color="#fff" />
      </View>
      <View style={styles.content}>
        <Text style={styles.amount} accessibilityElementsHidden={true}>
          {formatCurrency(animatedAmount)}
        </Text>
        <Text style={styles.label} accessibilityElementsHidden={true}>
          {t('dashboard.hero.saved')}
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    flex: 1,
    flexDirection: 'row',
    marginHorizontal: 4,
    padding: 16,
  },
  iconContainer: {
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    width: 40,
  },
  content: {
    flex: 1,
    marginLeft: 12,
  },
  amount: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '700',
  },
  label: {
    color: 'rgba(255, 255, 255, 0.8)',
    fontSize: 12,
    fontWeight: '500',
    marginTop: 2,
  },
});
