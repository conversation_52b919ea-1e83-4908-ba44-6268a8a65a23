import React, { useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  Animated,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, Award } from 'lucide-react-native';


interface Quote {
  id: string;
  text: string;
  author: string;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  unlockedAt: Date;
  isNew: boolean;
}

interface ColorScheme {
  primary: string;
  success: string;
  card: string;
  border: string;
  text: string;
  textSecondary: string;
  background: string;
}

interface MotivationSectionProps {
  quote?: Quote;
  recentAchievement?: Achievement;
  colors: ColorScheme;
  language: 'en' | 'nl';
}

export const MotivationSection: React.FC<MotivationSectionProps> = ({
  quote,
  recentAchievement,
  colors,
  language,
}) => {
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;

  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 800,
        useNativeDriver: true,
      }),
      Animated.spring(slideAnim, {
        toValue: 0,
        friction: 8,
        tension: 40,
        useNativeDriver: true,
      }),
    ]).start();
  }, [fadeAnim, slideAnim]);

  // Don't render if no content
  if (!quote && !recentAchievement) {
    return null;
  }

  return (
    <Animated.View
      style={[
        styles.container,
        {
          opacity: fadeAnim,
          transform: [{ translateY: slideAnim }],
        },
      ]}
    >
      {/* Daily Quote */}
      {quote && (
        <View
          style={[
            styles.quoteCard,
            { backgroundColor: colors.card, borderColor: colors.border },
          ]}
          accessibilityRole="text"
          accessibilityLabel={`${language === 'nl' ? 'Dagelijkse quote' : 'Daily quote'}: ${quote.text} - ${quote.author}`}
        >
          <View style={styles.quoteHeader}>
            <View style={[styles.quoteIcon, { backgroundColor: colors.primary + '20' }]}>
              <Sparkles size={20} color={colors.primary} />
            </View>
            <Text style={[styles.quoteTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Dagelijkse Quote' : 'Daily Quote'}
            </Text>
          </View>
          
          <Text style={[styles.quoteText, { color: colors.text }]}>
            "{quote.text}"
          </Text>
          
          <Text style={[styles.quoteAuthor, { color: colors.textSecondary }]}>
            — {quote.author}
          </Text>
        </View>
      )}

      {/* Recent Achievement */}
      {recentAchievement && (
        <View
          style={[
            styles.achievementCard,
            { backgroundColor: colors.card, borderColor: colors.border },
            recentAchievement.isNew && styles.newAchievement,
          ]}
          accessibilityRole="text"
          accessibilityLabel={`${language === 'nl' ? 'Prestatie' : 'Achievement'}: ${recentAchievement.title}. ${recentAchievement.description}`}
        >
          <View style={styles.achievementHeader}>
            <View style={[styles.achievementIcon, { backgroundColor: colors.success + '20' }]}>
              <Award size={20} color={colors.success} />
            </View>
            <View style={styles.achievementTitleContainer}>
              <Text style={[styles.achievementTitle, { color: colors.text }]}>
                {language === 'nl' ? 'Prestatie' : 'Achievement'}
              </Text>
              {recentAchievement.isNew && (
                <View style={[styles.newBadge, { backgroundColor: colors.success }]}>
                  <Text style={styles.newBadgeText}>
                    {language === 'nl' ? 'NIEUW' : 'NEW'}
                  </Text>
                </View>
              )}
            </View>
          </View>
          
          <View style={styles.achievementContent}>
            <Text style={styles.achievementEmoji}>
              {recentAchievement.icon}
            </Text>
            <View style={styles.achievementText}>
              <Text style={[styles.achievementName, { color: colors.text }]}>
                {recentAchievement.title}
              </Text>
              <Text style={[styles.achievementDescription, { color: colors.textSecondary }]}>
                {recentAchievement.description}
              </Text>
            </View>
          </View>
          
          <Text style={[styles.achievementDate, { color: colors.textSecondary }]}>
            {language === 'nl' ? 'Ontgrendeld op' : 'Unlocked on'} {recentAchievement.unlockedAt.toLocaleDateString(
              language === 'nl' ? 'nl-NL' : 'en-US',
              { month: 'long', day: 'numeric' }
            )}
          </Text>
        </View>
      )}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: 20,
    gap: 16,
  },
  quoteCard: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 3,
    overflow: 'hidden',
    padding: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  quoteHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 16,
  },
  quoteIcon: {
    alignItems: 'center',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    marginRight: 12,
    width: 40,
  },
  quoteTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  quoteText: {
    fontSize: 16,
    fontStyle: 'italic',
    lineHeight: 24,
    marginBottom: 12,
  },
  quoteAuthor: {
    fontSize: 14,
    fontStyle: 'italic',
    textAlign: 'right',
  },
  achievementCard: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 3,
    overflow: 'hidden',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
  },
  newAchievement: {
    borderWidth: 2,
    borderColor: '#FFD700',
  },
  achievementHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 16,
  },
  achievementIcon: {
    alignItems: 'center',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    marginRight: 12,
    width: 40,
  },
  achievementTitleContainer: {
    alignItems: 'center',
    flex: 1,
    flexDirection: 'row',
  },
  achievementTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  newBadge: {
    borderRadius: 8,
    marginLeft: 8,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  newBadgeText: {
    color: '#fff',
    fontSize: 10,
    fontWeight: '700',
    textTransform: 'uppercase',
  },
  achievementContent: {
    alignItems: 'center',
    flexDirection: 'row',
    marginBottom: 12,
  },
  achievementEmoji: {
    fontSize: 32,
    marginRight: 16,
  },
  achievementText: {
    flex: 1,
  },
  achievementName: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  achievementDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  achievementDate: {
    fontSize: 12,
    textAlign: 'center',
  },
});
