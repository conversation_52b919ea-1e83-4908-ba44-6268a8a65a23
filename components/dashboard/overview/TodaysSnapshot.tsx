import React from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  ScrollView,
} from 'react-native';
import { 
  Heart, 
  Activity, 
  TrendingUp, 
  TrendingDown, 
  Minus,
  ChevronRight 
} from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';


interface HealthSummary {
  completedMetrics: number;
  totalMetrics: number;
  topMetric?: {
    type: string;
    value: number;
    unit: string;
    progress: number;
  };
}

interface MoodTrend {
  current: number;
  trend: 'up' | 'down' | 'stable';
  weeklyAverage: number;
}

interface WeeklyProgress {
  checkInsCompleted: number;
  totalDays: number;
  streak: number;
}

interface ColorScheme {
  primary: string;
  success: string;
  warning: string;
  info: string;
  danger: string;
  card: string;
  border: string;
  text: string;
  textSecondary: string;
  background: string;
}

interface TodaysSnapshotProps {
  healthSummary: HealthSummary;
  moodTrend: MoodTrend;
  weeklyProgress: WeeklyProgress;
  colors: ColorScheme;
  language: 'en' | 'nl';
  onCardPress: (cardType: 'health' | 'mood' | 'progress') => void;
}

export const TodaysSnapshot: React.FC<TodaysSnapshotProps> = ({
  healthSummary,
  moodTrend,
  weeklyProgress,
  colors,
  language,
  onCardPress,
}) => {

  const getMoodEmoji = (mood: number) => {
    if (mood >= 4.5) return '😊';
    if (mood >= 3.5) return '🙂';
    if (mood >= 2.5) return '😐';
    if (mood >= 1.5) return '😔';
    return '😢';
  };

  const getTrendIcon = (trend: 'up' | 'down' | 'stable') => {
    switch (trend) {
      case 'up':
        return <TrendingUp size={16} color={colors.success} />;
      case 'down':
        return <TrendingDown size={16} color={colors.danger} />;
      default:
        return <Minus size={16} color={colors.info} />;
    }
  };

  const getHealthProgress = () => {
    if (healthSummary.totalMetrics === 0) return 0;
    return (healthSummary.completedMetrics / healthSummary.totalMetrics) * 100;
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 80) return colors.success;
    if (percentage >= 50) return colors.warning;
    return colors.danger;
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === 'nl' ? 'Vandaag\'s Overzicht' : 'Today\'s Snapshot'}
        </Text>
      </View>

      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={styles.cardsContainer}
      >
        {/* Health Summary Card */}
        <TouchableOpacity
          style={[styles.card, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => onCardPress('health')}
          accessibilityRole="button"
          accessibilityLabel={`${language === 'nl' ? 'Gezondheid' : 'Health'} - ${healthSummary.completedMetrics} ${language === 'nl' ? 'van' : 'of'} ${healthSummary.totalMetrics} ${language === 'nl' ? 'voltooid' : 'completed'}`}
        >
          <LinearGradient
            colors={[colors.success + '10', colors.success + '05']}
            style={styles.cardGradient}
          >
            <View style={styles.cardHeader}>
              <View style={[styles.cardIcon, { backgroundColor: colors.success + '20' }]}>
                <Heart size={20} color={colors.success} />
              </View>
              <ChevronRight size={16} color={colors.textSecondary} />
            </View>
            
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Gezondheid' : 'Health'}
            </Text>

            <View style={styles.healthStats}>
              <Text style={[styles.healthCount, { color: colors.text }]}>
                {healthSummary.completedMetrics}/{healthSummary.totalMetrics}
              </Text>
              <Text style={[styles.healthLabel, { color: colors.textSecondary }]}>
                {language === 'nl' ? 'metrieken voltooid' : 'metrics completed'}
              </Text>
            </View>

            {healthSummary.topMetric && (
              <View style={styles.topMetric}>
                <Text style={[styles.topMetricLabel, { color: colors.textSecondary }]}>
                  {language === 'nl' ? 'Top metriek' : 'Top metric'}
                </Text>
                <Text style={[styles.topMetricValue, { color: colors.text }]}>
                  {healthSummary.topMetric.value} {healthSummary.topMetric.unit}
                </Text>
              </View>
            )}

            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      backgroundColor: getProgressColor(getHealthProgress()),
                      width: `${getHealthProgress()}%`,
                    },
                  ]}
                />
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>

        {/* Mood Trend Card */}
        <TouchableOpacity
          style={[styles.card, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => onCardPress('mood')}
          accessibilityRole="button"
          accessibilityLabel={`${language === 'nl' ? 'Stemming' : 'Mood'} - ${language === 'nl' ? 'huidige' : 'current'} ${moodTrend.current}, ${language === 'nl' ? 'trend' : 'trend'} ${moodTrend.trend}`}
        >
          <LinearGradient
            colors={[colors.primary + '10', colors.primary + '05']}
            style={styles.cardGradient}
          >
            <View style={styles.cardHeader}>
              <View style={[styles.cardIcon, { backgroundColor: colors.primary + '20' }]}>
                <Activity size={20} color={colors.primary} />
              </View>
              <ChevronRight size={16} color={colors.textSecondary} />
            </View>
            
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Stemming' : 'Mood'}
            </Text>
            
            <View style={styles.moodDisplay}>
              <Text style={styles.moodEmoji}>
                {getMoodEmoji(moodTrend.current)}
              </Text>
              <View style={styles.moodStats}>
                <Text style={[styles.moodValue, { color: colors.text }]}>
                  {moodTrend.current.toFixed(1)}
                </Text>
                <View style={styles.trendContainer}>
                  {getTrendIcon(moodTrend.trend)}
                  <Text style={[styles.trendText, { color: colors.textSecondary }]}>
                    {language === 'nl' ?
                      (moodTrend.trend === 'up' ? 'stijgend' : moodTrend.trend === 'down' ? 'dalend' : 'stabiel') :
                      (moodTrend.trend === 'up' ? 'rising' : moodTrend.trend === 'down' ? 'falling' : 'stable')
                    }
                  </Text>
                </View>
              </View>
            </View>

            <View style={styles.weeklyAverage}>
              <Text style={[styles.weeklyLabel, { color: colors.textSecondary }]}>
                {language === 'nl' ? 'Wekelijks gemiddelde' : 'Weekly average'}
              </Text>
              <Text style={[styles.weeklyValue, { color: colors.text }]}>
                {moodTrend.weeklyAverage.toFixed(1)}
              </Text>
            </View>
          </LinearGradient>
        </TouchableOpacity>

        {/* Weekly Progress Card */}
        <TouchableOpacity
          style={[styles.card, { backgroundColor: colors.card, borderColor: colors.border }]}
          onPress={() => onCardPress('progress')}
          accessibilityRole="button"
          accessibilityLabel={`${language === 'nl' ? 'Wekelijkse voortgang' : 'Weekly progress'} - ${weeklyProgress.checkInsCompleted} ${language === 'nl' ? 'van' : 'of'} ${weeklyProgress.totalDays} ${language === 'nl' ? 'check-ins voltooid' : 'check-ins completed'}`}
        >
          <LinearGradient
            colors={[colors.warning + '10', colors.warning + '05']}
            style={styles.cardGradient}
          >
            <View style={styles.cardHeader}>
              <View style={[styles.cardIcon, { backgroundColor: colors.warning + '20' }]}>
                <TrendingUp size={20} color={colors.warning} />
              </View>
              <ChevronRight size={16} color={colors.textSecondary} />
            </View>
            
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              {t('dashboard.weeklyProgress')}
            </Text>
            
            <View style={styles.progressStats}>
              <Text style={[styles.progressCount, { color: colors.text }]}>
                {weeklyProgress.checkInsCompleted}/{weeklyProgress.totalDays}
              </Text>
              <Text style={[styles.progressLabel, { color: colors.textSecondary }]}>
                {language === 'nl' ? 'check-ins voltooid' : 'check-ins completed'}
              </Text>
            </View>

            <View style={styles.streakContainer}>
              <Text style={[styles.streakLabel, { color: colors.textSecondary }]}>
                {language === 'nl' ? 'Huidige reeks' : 'Current streak'}
              </Text>
              <Text style={[styles.streakValue, { color: colors.text }]}>
                {weeklyProgress.streak} {language === 'nl' ? 'dagen' : 'days'}
              </Text>
            </View>

            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: colors.border }]}>
                <View
                  style={[
                    styles.progressFill,
                    {
                      backgroundColor: colors.warning,
                      width: `${(weeklyProgress.checkInsCompleted / weeklyProgress.totalDays) * 100}%`,
                    },
                  ]}
                />
              </View>
            </View>
          </LinearGradient>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: 24,
  },
  header: {
    paddingHorizontal: 20,
    marginBottom: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
  },
  cardsContainer: {
    paddingHorizontal: 20,
    gap: 16,
  },
  card: {
    borderRadius: 20,
    borderWidth: 1,
    elevation: 3,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    width: 280,
  },
  cardGradient: {
    padding: 20,
  },
  cardHeader: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  cardIcon: {
    alignItems: 'center',
    borderRadius: 12,
    height: 40,
    justifyContent: 'center',
    width: 40,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 16,
  },
  healthStats: {
    alignItems: 'center',
    marginBottom: 16,
  },
  healthCount: {
    fontSize: 32,
    fontWeight: '800',
  },
  healthLabel: {
    fontSize: 14,
    marginTop: 4,
  },
  topMetric: {
    alignItems: 'center',
    marginBottom: 16,
  },
  topMetricLabel: {
    fontSize: 12,
  },
  topMetricValue: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 2,
  },
  moodDisplay: {
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 16,
  },
  moodEmoji: {
    fontSize: 32,
    marginRight: 16,
  },
  moodStats: {
    alignItems: 'center',
  },
  moodValue: {
    fontSize: 24,
    fontWeight: '700',
  },
  trendContainer: {
    alignItems: 'center',
    flexDirection: 'row',
    marginTop: 4,
  },
  trendText: {
    fontSize: 12,
    marginLeft: 4,
  },
  weeklyAverage: {
    alignItems: 'center',
    marginBottom: 16,
  },
  weeklyLabel: {
    fontSize: 12,
  },
  weeklyValue: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 2,
  },
  progressStats: {
    alignItems: 'center',
    marginBottom: 16,
  },
  progressCount: {
    fontSize: 32,
    fontWeight: '800',
  },
  progressLabel: {
    fontSize: 14,
    marginTop: 4,
  },
  streakContainer: {
    alignItems: 'center',
    marginBottom: 16,
  },
  streakLabel: {
    fontSize: 12,
  },
  streakValue: {
    fontSize: 16,
    fontWeight: '600',
    marginTop: 2,
  },
  progressContainer: {
    marginTop: 8,
  },
  progressBar: {
    borderRadius: 4,
    height: 8,
    overflow: 'hidden',
  },
  progressFill: {
    borderRadius: 4,
    height: 8,
  },
});
