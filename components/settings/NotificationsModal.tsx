import React from "react";
import { useState, useEffect, useMemo } from 'react';
import { StyleSheet, Text, View, ScrollView, Modal, TouchableOpacity, Switch, Platform } from 'react-native';
import { Bell, Calendar, AlertTriangle, BookOpen, Brain, BarChart3, Users, X, Save, Clock } from 'lucide-react-native';
import DateTimePicker from '@react-native-community/datetimepicker';
import { useUserStore } from '@/store/user/user-store';
import { UserProfile } from '@/types/user';
import { useTranslation } from '@/hooks/useTranslation';
import { scheduleDailyCheckinNotification } from '@/utils/system/notification-utils';

interface SettingsColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
  success?: string;
  danger?: string;
  info?: string;
  secondary?: string;
  warning?: string;
  accent?: string;
}

interface NotificationsModalProps {
  visible: boolean;
  onClose: () => void;
  profile: UserProfile;
  colors: SettingsColorScheme;
}

interface NotificationSettings {
  dailyReminders: boolean;
  dailyReminderTime: string;
  milestoneAlerts: boolean;
  emergencyAlerts: boolean;
  journalReminders: boolean;
  mindfulnessReminders: boolean;
  weeklyReports: boolean;
  communityMessages: boolean;
}

export const NotificationsModal: React.FC<NotificationsModalProps> = ({
  visible,
  onClose,
  profile,
  colors,
}) => {
  const { updateProfile } = useUserStore();
  const { t } = useTranslation();
  
  // Initialize notification settings
  const initialSettings: NotificationSettings = useMemo(() => ({
    dailyReminders: true,
    dailyReminderTime: "20:00",
    milestoneAlerts: true,
    emergencyAlerts: true,
    journalReminders: true,
    mindfulnessReminders: true,
    weeklyReports: true,
    communityMessages: false,
  }), []);

  const [settings, setSettings] = useState<NotificationSettings>(
    { ...initialSettings, ...profile.notificationSettings }
  );

  // Time picker state
  const [showTimePicker, setShowTimePicker] = useState(false);

  // Load notification settings from profile when modal opens
  useEffect(() => {
    if (visible && profile.notificationSettings) {
      setSettings({ ...initialSettings, ...profile.notificationSettings });
    }
  }, [visible, profile, initialSettings]);

  const handleToggle = (id: string, value: boolean) => {
    setSettings(prev => ({ ...prev, [id]: value }));
  };

  const handleTimeChange = (event: any, selectedTime?: Date) => {
    setShowTimePicker(Platform.OS === 'ios');
    if (selectedTime) {
      const timeString = selectedTime.toTimeString().slice(0, 5); // Format: "HH:MM"
      setSettings(prev => ({ ...prev, dailyReminderTime: timeString }));
    }
  };

  const getTimeFromString = (timeString: string): Date => {
    const [hours, minutes] = timeString.split(':').map(Number);
    const date = new Date();
    date.setHours(hours, minutes, 0, 0);
    return date;
  };

  const formatTimeForDisplay = (timeString: string): string => {
    const time = getTimeFromString(timeString);
    return time.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const saveNotificationSettings = async () => {
    updateProfile({ notificationSettings: settings });

    // Reschedule notifications with new settings
    try {
      await scheduleDailyCheckinNotification();
    } catch (error) {
      console.error('Failed to reschedule notifications:', error);
    }

    onClose();
  };

  const notificationSections = [
    {
      title: t('settings.notificationsModal.dailyReminders'),
      items: [
        {
          id: 'dailyReminders' as keyof NotificationSettings,
          title: t('settings.notificationsModal.dailyCheckIn'),
          description: t('settings.notificationsModal.dailyCheckInDesc'),
          icon: Calendar,
          value: settings.dailyReminders,
          color: colors.primary
        },
        {
          id: 'milestoneAlerts' as keyof NotificationSettings,
          title: t('settings.notificationsModal.milestoneAlerts'),
          description: t('settings.notificationsModal.milestoneAlertsDesc'),
          icon: Bell,
          value: settings.milestoneAlerts,
          color: colors.success || colors.primary
        },
        {
          id: 'emergencyAlerts' as keyof NotificationSettings,
          title: t('settings.notificationsModal.emergencyAlerts'),
          description: t('settings.notificationsModal.emergencyAlertsDesc'),
          icon: AlertTriangle,
          value: settings.emergencyAlerts,
          color: colors.danger || colors.primary
        }
      ]
    },
    {
      title: t('settings.notificationsModal.activities'),
      items: [
        {
          id: 'journalReminders' as keyof NotificationSettings,
          title: t('settings.notificationsModal.journalReminders'),
          description: t('settings.notificationsModal.journalRemindersDesc'),
          icon: BookOpen,
          value: settings.journalReminders,
          color: colors.info || colors.primary
        },
        {
          id: 'mindfulnessReminders' as keyof NotificationSettings,
          title: t('settings.notificationsModal.mindfulnessReminders'),
          description: t('settings.notificationsModal.mindfulnessRemindersDesc'),
          icon: Brain,
          value: settings.mindfulnessReminders,
          color: colors.secondary || colors.primary
        }
      ]
    },
    {
      title: t('settings.notificationsModal.other'),
      items: [
        {
          id: 'weeklyReports' as keyof NotificationSettings,
          title: t('settings.notificationsModal.weeklyReports'),
          description: t('settings.notificationsModal.weeklyReportsDesc'),
          icon: BarChart3,
          value: settings.weeklyReports,
          color: colors.warning || colors.primary
        },
        {
          id: 'communityMessages' as keyof NotificationSettings,
          title: t('settings.notificationsModal.communityMessages'),
          description: t('settings.notificationsModal.communityMessagesDesc'),
          icon: Users,
          value: settings.communityMessages,
          color: colors.accent || colors.primary
        }
      ]
    }
  ];

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={[styles.modalContent, { backgroundColor: colors.card }]}>
          {/* Header */}
          <View style={[styles.modalHeader, { borderBottomColor: colors.border }]}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>
              {t('settings.notificationsModal.title')}
            </Text>
            <TouchableOpacity onPress={onClose} style={styles.closeButton}>
              <X size={24} color={colors.text} strokeWidth={2.5} />
            </TouchableOpacity>
          </View>
          
          <ScrollView style={styles.modalBody} showsVerticalScrollIndicator={false}>
            {notificationSections.map((section, sectionIndex) => (
              <View key={sectionIndex} style={styles.notificationSection}>
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  {section.title}
                </Text>
                
                {section.items.map((item) => (
                  <View key={item.id}>
                    <View style={[styles.toggleItem, { backgroundColor: colors.background, borderColor: colors.border }]}>
                      <View style={[styles.iconContainer, { backgroundColor: item.color + '20' }]}>
                        <item.icon size={20} color={item.color} />
                      </View>
                      <View style={styles.itemContent}>
                        <Text style={[styles.itemTitle, { color: colors.text }]}>
                          {item.title}
                        </Text>
                        <Text style={[styles.itemDescription, { color: colors.muted }]}>
                          {item.description}
                        </Text>
                      </View>
                      <Switch
                        value={item.value}
                        onValueChange={(value) => handleToggle(item.id, value)}
                        trackColor={{ false: colors.border, true: colors.primary + '40' }}
                        thumbColor={item.value ? colors.primary : colors.muted}
                      />
                    </View>

                    {/* Time picker for daily reminders */}
                    {item.id === 'dailyReminders' && settings.dailyReminders && (
                      <TouchableOpacity
                        style={[styles.timePickerItem, { backgroundColor: colors.background, borderColor: colors.border }]}
                        onPress={() => setShowTimePicker(true)}
                      >
                        <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
                          <Clock size={20} color={colors.primary} />
                        </View>
                        <View style={styles.itemContent}>
                          <Text style={[styles.itemTitle, { color: colors.text }]}>
                            {profile.language === 'nl' ? 'Herinneringstijd' : 'Reminder Time'}
                          </Text>
                          <Text style={[styles.itemDescription, { color: colors.muted }]}>
                            {profile.language === 'nl' ? 'Stel de tijd in voor dagelijkse herinneringen' : 'Set the time for daily reminders'}
                          </Text>
                        </View>
                        <Text style={[styles.timeDisplay, { color: colors.primary }]}>
                          {formatTimeForDisplay(settings.dailyReminderTime)}
                        </Text>
                      </TouchableOpacity>
                    )}
                  </View>
                ))}
              </View>
            ))}
          </ScrollView>

          {/* Time Picker */}
          {showTimePicker && (
            <DateTimePicker
              value={getTimeFromString(settings.dailyReminderTime)}
              mode="time"
              is24Hour={true}
              display={Platform.OS === 'ios' ? 'spinner' : 'default'}
              onChange={handleTimeChange}
            />
          )}

          {/* Footer */}
          <View style={[styles.modalFooter, { borderTopColor: colors.border }]}>
            <TouchableOpacity
              style={[styles.cancelButton, { borderColor: colors.border }]}
              onPress={onClose}
            >
              <Text style={[styles.cancelButtonText, { color: colors.text }]}>
                {t('common.cancel')}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.saveButton, { backgroundColor: colors.primary }]}
              onPress={saveNotificationSettings}
            >
              <Save size={20} color="#fff" />
              <Text style={styles.saveButtonText}>
                {t('common.save')}
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  cancelButton: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    justifyContent: 'center',
    marginRight: 8,
    padding: 16,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '500',
  },
  closeButton: {
    padding: 4,
  },
  iconContainer: {
    alignItems: 'center',
    borderRadius: 20,
    height: 40,
    justifyContent: 'center',
    marginRight: 16,
    width: 40,
  },
  itemContent: {
    flex: 1,
  },
  itemDescription: {
    fontSize: 14,
    lineHeight: 20,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 4,
  },
  modalBody: {
    maxHeight: 400,
    padding: 16,
  },
  modalContent: {
    borderRadius: 24,
    elevation: 8,
    maxHeight: '80%',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.25,
    shadowRadius: 20,
    width: '100%',
  },
  modalFooter: {
    borderTopWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
  },
  modalHeader: {
    alignItems: 'center',
    borderBottomWidth: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 20,
  },
  modalOverlay: {
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    letterSpacing: -0.3,
  },
  notificationSection: {
    marginBottom: 24,
  },
  saveButton: {
    alignItems: 'center',
    borderRadius: 12,
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'center',
    marginLeft: 8,
    padding: 16,
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '500',
    marginLeft: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  timeDisplay: {
    fontSize: 16,
    fontWeight: '600',
  },
  timePickerItem: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 12,
    marginLeft: 16,
    marginTop: 8,
    padding: 16,
  },
  toggleItem: {
    alignItems: 'center',
    borderRadius: 12,
    borderWidth: 1,
    flexDirection: 'row',
    marginBottom: 12,
    padding: 16,
  },
});