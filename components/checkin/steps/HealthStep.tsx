import React from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import { Bed, Droplet, Bike, Pill, Plus, Minus } from 'lucide-react-native';
import { CheckInStepProps } from '../types';

const HEALTH_METRICS = [
  {
    key: 'sleep' as const,
    icon: Bed,
    color: '#8B5CF6',
    labelNL: 'Slaap',
    labelEN: 'Sleep',
    unitNL: 'uren',
    unitEN: 'hours',
    placeholderNL: '8',
    placeholderEN: '8',
    max: 12,
    step: 0.5,
  },
  {
    key: 'hydration' as const,
    icon: Droplet,
    color: '#06B6D4',
    labelNL: 'Water',
    labelEN: 'Water',
    unitNL: 'glazen',
    unitEN: 'glasses',
    placeholderNL: '8',
    placeholderEN: '8',
    max: 20,
    step: 1,
  },
  {
    key: 'exercise' as const,
    icon: Bike,
    color: '#10B981',
    labelNL: 'Beweging',
    labelEN: 'Exercise',
    unitNL: 'minuten',
    unitEN: 'minutes',
    placeholderNL: '30',
    placeholderEN: '30',
    max: 300,
    step: 5,
  },
  {
    key: 'pills' as const,
    icon: Pill,
    color: '#F59E0B',
    labelNL: 'Medicatie',
    labelEN: 'Medication',
    unitNL: 'pillen',
    unitEN: 'pills',
    placeholderNL: '0',
    placeholderEN: '0',
    max: 10,
    step: 1,
  },
];

export const HealthStep: React.FC<CheckInStepProps> = ({
  checkInData,
  updateCheckInData,
  colors,
  language,
}) => {
  const handleMetricChange = (key: keyof typeof checkInData.healthMetrics, value: number) => {
    updateCheckInData({
      healthMetrics: {
        ...checkInData.healthMetrics,
        [key]: Math.max(0, value),
      },
    });
  };

  const incrementMetric = (key: keyof typeof checkInData.healthMetrics, step: number, max: number) => {
    const currentValue = checkInData.healthMetrics[key];
    const newValue = Math.min(max, currentValue + step);
    handleMetricChange(key, newValue);
  };

  const decrementMetric = (key: keyof typeof checkInData.healthMetrics, step: number) => {
    const currentValue = checkInData.healthMetrics[key];
    const newValue = Math.max(0, currentValue - step);
    handleMetricChange(key, newValue);
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === 'nl' ? 'Hoe ging je gezondheid vandaag?' : 'How was your health today?'}
        </Text>
        <Text style={[styles.subtitle, { color: colors.muted }]}>
          {language === 'nl' 
            ? 'Vul in wat je vandaag hebt gedaan voor je gezondheid'
            : 'Track what you did for your health today'
          }
        </Text>
      </View>

      <View style={styles.metricsContainer}>
        {HEALTH_METRICS.map((metric) => {
          const IconComponent = metric.icon;
          const currentValue = checkInData.healthMetrics[metric.key];
          
          return (
            <View key={metric.key} style={[styles.metricCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
              <View style={styles.metricHeader}>
                <View style={[styles.iconContainer, { backgroundColor: metric.color + '20' }]}>
                  <IconComponent size={24} color={metric.color} />
                </View>
                <View style={styles.metricInfo}>
                  <Text style={[styles.metricLabel, { color: colors.text }]}>
                    {language === 'nl' ? metric.labelNL : metric.labelEN}
                  </Text>
                  <Text style={[styles.metricUnit, { color: colors.muted }]}>
                    {language === 'nl' ? metric.unitNL : metric.unitEN}
                  </Text>
                </View>
              </View>

              <View style={styles.metricControls}>
                <TouchableOpacity
                  style={[styles.controlButton, { backgroundColor: colors.background }]}
                  onPress={() => decrementMetric(metric.key, metric.step)}
                  disabled={currentValue <= 0}
                >
                  <Minus 
                    size={20} 
                    color={currentValue <= 0 ? colors.muted : colors.text} 
                  />
                </TouchableOpacity>

                <View style={styles.valueContainer}>
                  <TextInput
                    style={[styles.valueInput, { color: colors.text, borderColor: colors.border }]}
                    value={currentValue.toString()}
                    onChangeText={(text) => {
                      const value = parseFloat(text) || 0;
                      handleMetricChange(metric.key, Math.min(metric.max, value));
                    }}
                    keyboardType="numeric"
                    textAlign="center"
                  />
                  <Text style={[styles.valueUnit, { color: colors.muted }]}>
                    {language === 'nl' ? metric.unitNL : metric.unitEN}
                  </Text>
                </View>

                <TouchableOpacity
                  style={[styles.controlButton, { backgroundColor: colors.background }]}
                  onPress={() => incrementMetric(metric.key, metric.step, metric.max)}
                  disabled={currentValue >= metric.max}
                >
                  <Plus 
                    size={20} 
                    color={currentValue >= metric.max ? colors.muted : colors.text} 
                  />
                </TouchableOpacity>
              </View>

              {/* Progress indicator */}
              <View style={styles.progressContainer}>
                <View style={[styles.progressBackground, { backgroundColor: colors.border }]}>
                  <View
                    style={[
                      styles.progressFill,
                      {
                        backgroundColor: metric.color,
                        width: `${Math.min(100, (currentValue / metric.max) * 100)}%`,
                      },
                    ]}
                  />
                </View>
                <Text style={[styles.progressText, { color: colors.muted }]}>
                  {currentValue} / {metric.max}
                </Text>
              </View>
            </View>
          );
        })}
      </View>

      <View style={[styles.tipContainer, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}>
        <Text style={[styles.tipText, { color: colors.text }]}>
          💡 {language === 'nl' 
            ? 'Tip: Kleine stappen leiden tot grote veranderingen. Elke inspanning telt!'
            : 'Tip: Small steps lead to big changes. Every effort counts!'
          }
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: 24,
  },
  header: {
    gap: 8,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
  },
  metricsContainer: {
    gap: 16,
  },
  metricCard: {
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    gap: 16,
  },
  metricHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  metricInfo: {
    flex: 1,
  },
  metricLabel: {
    fontSize: 18,
    fontWeight: '600',
  },
  metricUnit: {
    fontSize: 14,
    marginTop: 2,
  },
  metricControls: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    alignItems: 'center',
    justifyContent: 'center',
  },
  valueContainer: {
    flex: 1,
    alignItems: 'center',
    gap: 4,
  },
  valueInput: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    borderBottomWidth: 2,
    paddingVertical: 8,
    paddingHorizontal: 16,
    minWidth: 80,
  },
  valueUnit: {
    fontSize: 12,
    fontWeight: '500',
  },
  progressContainer: {
    gap: 8,
  },
  progressBackground: {
    height: 6,
    borderRadius: 3,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 3,
  },
  progressText: {
    fontSize: 12,
    textAlign: 'center',
  },
  tipContainer: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});
