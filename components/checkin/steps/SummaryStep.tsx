import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { CheckCircle, Heart, Target, Droplet, Bed, Bike, Pill, Calendar, BookOpen, Sparkles } from 'lucide-react-native';
import { CheckInStepProps } from '../types';

const MOOD_LABELS = {
  nl: ['Slecht', 'Matig', 'Oké', 'Goed', 'Uitstekend'],
  en: ['Poor', 'Fair', 'Okay', 'Good', 'Excellent'],
};

const CRAVING_LABELS = {
  nl: ['Geen', 'Licht', 'Matig', 'Sterk', 'Intens'],
  en: ['None', 'Light', 'Moderate', 'Strong', 'Intense'],
};

const HEALTH_METRICS = {
  sleep: { icon: Bed, labelNL: 'Slaap', labelEN: 'Sleep', unitNL: 'uren', unitEN: 'hours' },
  hydration: { icon: Droplet, labelNL: 'Water', labelEN: 'Water', unitNL: 'glazen', unitEN: 'glasses' },
  exercise: { icon: Bike, labelNL: 'Beweging', labelEN: 'Exercise', unitNL: 'minuten', unitEN: 'minutes' },
  pills: { icon: Pill, labelNL: 'Medicatie', labelEN: 'Medication', unitNL: 'pillen', unitEN: 'pills' },
};

export const SummaryStep: React.FC<CheckInStepProps & { onComplete: () => void }> = ({
  checkInData,
  colors,
  language,
  onComplete,
}) => {
  const moodLabels = MOOD_LABELS[language as keyof typeof MOOD_LABELS] || MOOD_LABELS.en;
  const cravingLabels = CRAVING_LABELS[language as keyof typeof CRAVING_LABELS] || CRAVING_LABELS.en;

  const getMoodEmoji = (mood: number) => {
    const emojis = ['😢', '😕', '😐', '😊', '😄'];
    return emojis[mood - 1] || '😐';
  };

  const getMotivationalMessage = () => {
    if (language === 'nl') {
      return [
        'Geweldig gedaan! Je hebt weer een dag succesvol afgerond.',
        'Elke dag dat je je check-in doet, investeer je in jezelf.',
        'Je bent op de goede weg. Blijf zo doorgaan!',
        'Trots op je inzet voor je herstel en welzijn.',
      ];
    } else {
      return [
        'Great job! You\'ve successfully completed another day.',
        'Every day you do your check-in, you\'re investing in yourself.',
        'You\'re on the right track. Keep it up!',
        'Proud of your commitment to your recovery and wellbeing.',
      ];
    }
  };

  const motivationalMessages = getMotivationalMessage();
  const randomMessage = motivationalMessages[Math.floor(Math.random() * motivationalMessages.length)];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <View style={[styles.iconContainer, { backgroundColor: colors.success + '20' }]}>
          <CheckCircle size={48} color={colors.success || colors.primary} />
        </View>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === 'nl' ? 'Check-in voltooid!' : 'Check-in complete!'}
        </Text>
        <Text style={[styles.subtitle, { color: colors.muted }]}>
          {language === 'nl' 
            ? 'Hier is een overzicht van je dag'
            : 'Here\'s a summary of your day'
          }
        </Text>
      </View>

      {/* Mood Summary */}
      <View style={[styles.summaryCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <View style={styles.cardHeader}>
          <Heart size={24} color={colors.primary} />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Stemming & Verlangen' : 'Mood & Cravings'}
          </Text>
        </View>
        <View style={styles.cardContent}>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.muted }]}>
              {language === 'nl' ? 'Stemming:' : 'Mood:'}
            </Text>
            <View style={styles.summaryValue}>
              <Text style={styles.moodEmoji}>{getMoodEmoji(checkInData.mood)}</Text>
              <Text style={[styles.summaryText, { color: colors.text }]}>
                {moodLabels[checkInData.mood - 1]}
              </Text>
            </View>
          </View>
          <View style={styles.summaryRow}>
            <Text style={[styles.summaryLabel, { color: colors.muted }]}>
              {language === 'nl' ? 'Verlangen:' : 'Cravings:'}
            </Text>
            <Text style={[styles.summaryText, { color: colors.text }]}>
              {cravingLabels[checkInData.cravingIntensity - 1]}
            </Text>
          </View>
          {checkInData.moodNotes && (
            <View style={styles.notesContainer}>
              <Text style={[styles.notesLabel, { color: colors.muted }]}>
                {language === 'nl' ? 'Notities:' : 'Notes:'}
              </Text>
              <Text style={[styles.notesText, { color: colors.text }]}>
                {checkInData.moodNotes}
              </Text>
            </View>
          )}
        </View>
      </View>

      {/* Health Summary */}
      <View style={[styles.summaryCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
        <View style={styles.cardHeader}>
          <Target size={24} color={colors.primary} />
          <Text style={[styles.cardTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Gezondheid' : 'Health'}
          </Text>
        </View>
        <View style={styles.cardContent}>
          {Object.entries(checkInData.healthMetrics).map(([key, value]) => {
            if (value <= 0) return null;
            const metric = HEALTH_METRICS[key as keyof typeof HEALTH_METRICS];
            const IconComponent = metric.icon;
            
            return (
              <View key={key} style={styles.healthRow}>
                <IconComponent size={20} color={colors.primary} />
                <Text style={[styles.healthLabel, { color: colors.text }]}>
                  {language === 'nl' ? metric.labelNL : metric.labelEN}:
                </Text>
                <Text style={[styles.healthValue, { color: colors.text }]}>
                  {value} {language === 'nl' ? metric.unitNL : metric.unitEN}
                </Text>
              </View>
            );
          })}
        </View>
      </View>

      {/* Goals Summary */}
      {checkInData.dailyGoals.length > 0 && (
        <View style={[styles.summaryCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <View style={styles.cardHeader}>
            <Target size={24} color={colors.primary} />
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Doelen voor vandaag' : 'Today\'s goals'}
            </Text>
          </View>
          <View style={styles.cardContent}>
            {checkInData.dailyGoals.map((goal, index) => (
              <View key={index} style={styles.goalRow}>
                <CheckCircle size={16} color={colors.success || colors.primary} />
                <Text style={[styles.goalText, { color: colors.text }]}>
                  {goal}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Reflection Summary */}
      {checkInData.reflection && (
        <View style={[styles.summaryCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
          <View style={styles.cardHeader}>
            <BookOpen size={24} color={colors.primary} />
            <Text style={[styles.cardTitle, { color: colors.text }]}>
              {language === 'nl' ? 'Reflectie' : 'Reflection'}
            </Text>
          </View>
          <View style={styles.cardContent}>
            <Text style={[styles.reflectionText, { color: colors.text }]}>
              {checkInData.reflection}
            </Text>
          </View>
        </View>
      )}

      {/* Motivational Message */}
      <View style={[styles.motivationCard, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}>
        <Sparkles size={24} color={colors.primary} />
        <Text style={[styles.motivationText, { color: colors.text }]}>
          {randomMessage}
        </Text>
      </View>

      {/* Complete Button */}
      <TouchableOpacity
        style={[styles.completeButton, { backgroundColor: colors.primary }]}
        onPress={onComplete}
      >
        <CheckCircle size={24} color="#fff" />
        <Text style={styles.completeButtonText}>
          {language === 'nl' ? 'Check-in voltooien' : 'Complete check-in'}
        </Text>
      </TouchableOpacity>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    marginBottom: 32,
    gap: 12,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
  },
  summaryCard: {
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 16,
    overflow: 'hidden',
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    padding: 20,
    paddingBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  cardContent: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    gap: 12,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  summaryLabel: {
    fontSize: 16,
    fontWeight: '500',
  },
  summaryValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  summaryText: {
    fontSize: 16,
    fontWeight: '600',
  },
  moodEmoji: {
    fontSize: 20,
  },
  notesContainer: {
    marginTop: 8,
    gap: 8,
  },
  notesLabel: {
    fontSize: 14,
    fontWeight: '500',
  },
  notesText: {
    fontSize: 14,
    lineHeight: 20,
  },
  healthRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  healthLabel: {
    fontSize: 16,
    flex: 1,
  },
  healthValue: {
    fontSize: 16,
    fontWeight: '600',
  },
  goalRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  goalText: {
    fontSize: 16,
    flex: 1,
  },
  reflectionText: {
    fontSize: 16,
    lineHeight: 24,
  },
  motivationCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 16,
    borderWidth: 1,
    marginBottom: 24,
    gap: 12,
  },
  motivationText: {
    fontSize: 16,
    fontWeight: '500',
    flex: 1,
    lineHeight: 24,
  },
  completeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    borderRadius: 16,
    gap: 12,
    marginBottom: 20,
  },
  completeButtonText: {
    color: '#fff',
    fontSize: 18,
    fontWeight: '700',
  },
});
