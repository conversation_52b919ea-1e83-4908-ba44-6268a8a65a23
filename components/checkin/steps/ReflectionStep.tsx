import React from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity } from 'react-native';
import { BookO<PERSON>, Lightbulb, Heart, Star } from 'lucide-react-native';
import { CheckInStepProps } from '../types';

const REFLECTION_PROMPTS = {
  nl: [
    'Wat ging er goed vandaag?',
    'Waar ben je dankbaar voor?',
    'Wat heb je geleerd?',
    'Hoe heb je je herstel ondersteund?',
    'Wat wil je morgen anders doen?',
    'Welke uitdaging heb je overwonnen?',
  ],
  en: [
    'What went well today?',
    'What are you grateful for?',
    'What did you learn?',
    'How did you support your recovery?',
    'What would you do differently tomorrow?',
    'What challenge did you overcome?',
  ],
};

export const ReflectionStep: React.FC<CheckInStepProps> = ({
  checkInData,
  updateCheckInData,
  colors,
  language,
}) => {
  const prompts = REFLECTION_PROMPTS[language as keyof typeof REFLECTION_PROMPTS] || REFLECTION_PROMPTS.en;

  const handleReflectionChange = (reflection: string) => {
    updateCheckInData({ reflection });
  };

  const addPromptToReflection = (prompt: string) => {
    const currentReflection = checkInData.reflection;
    const newReflection = currentReflection 
      ? `${currentReflection}\n\n${prompt} `
      : `${prompt} `;
    
    updateCheckInData({ reflection: newReflection });
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <BookOpen size={28} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            {language === 'nl' ? 'Reflectie op je dag' : 'Reflect on your day'}
          </Text>
        </View>
        <Text style={[styles.subtitle, { color: colors.muted }]}>
          {language === 'nl' 
            ? 'Neem even de tijd om na te denken over je dag en je gevoelens'
            : 'Take a moment to think about your day and your feelings'
          }
        </Text>
      </View>

      <View style={styles.promptsSection}>
        <View style={styles.promptsHeader}>
          <Lightbulb size={20} color={colors.warning || colors.primary} />
          <Text style={[styles.promptsTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Reflectievragen' : 'Reflection prompts'}
          </Text>
        </View>
        <Text style={[styles.promptsSubtitle, { color: colors.muted }]}>
          {language === 'nl' 
            ? 'Tik op een vraag om deze toe te voegen aan je reflectie'
            : 'Tap on a question to add it to your reflection'
          }
        </Text>
        
        <View style={styles.promptsList}>
          {prompts.map((prompt, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.promptItem,
                {
                  backgroundColor: colors.card,
                  borderColor: colors.border,
                },
              ]}
              onPress={() => addPromptToReflection(prompt)}
            >
              <Text style={[styles.promptText, { color: colors.text }]}>
                {prompt}
              </Text>
              <View style={[styles.promptIcon, { backgroundColor: colors.primary + '20' }]}>
                <Star size={16} color={colors.primary} />
              </View>
            </TouchableOpacity>
          ))}
        </View>
      </View>

      <View style={styles.reflectionSection}>
        <View style={styles.reflectionHeader}>
          <Heart size={20} color={colors.primary} />
          <Text style={[styles.reflectionTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Je reflectie' : 'Your reflection'}
          </Text>
        </View>
        
        <TextInput
          style={[
            styles.reflectionInput,
            {
              backgroundColor: colors.card,
              borderColor: colors.border,
              color: colors.text,
            },
          ]}
          placeholder={
            language === 'nl'
              ? 'Schrijf hier je gedachten over de dag... Hoe voel je je? Wat heb je geleerd? Waar ben je dankbaar voor?'
              : 'Write your thoughts about the day here... How do you feel? What did you learn? What are you grateful for?'
          }
          placeholderTextColor={colors.muted}
          value={checkInData.reflection}
          onChangeText={handleReflectionChange}
          multiline
          numberOfLines={8}
          textAlignVertical="top"
        />
      </View>

      <View style={[styles.tipContainer, { backgroundColor: colors.success + '10', borderColor: colors.success + '30' }]}>
        <Text style={[styles.tipText, { color: colors.text }]}>
          💭 {language === 'nl' 
            ? 'Reflectie helpt je om patronen te herkennen en je groei bij te houden'
            : 'Reflection helps you recognize patterns and track your growth'
          }
        </Text>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: 24,
  },
  header: {
    gap: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    flex: 1,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
  },
  promptsSection: {
    gap: 16,
  },
  promptsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  promptsTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  promptsSubtitle: {
    fontSize: 14,
    fontStyle: 'italic',
  },
  promptsList: {
    gap: 8,
  },
  promptItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  promptText: {
    fontSize: 15,
    flex: 1,
    marginRight: 12,
  },
  promptIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  reflectionSection: {
    gap: 16,
  },
  reflectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  reflectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  reflectionInput: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 16,
    fontSize: 16,
    minHeight: 150,
    maxHeight: 200,
  },
  tipContainer: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  tipText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});
