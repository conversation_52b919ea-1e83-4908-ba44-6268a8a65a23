import React, { useState } from 'react';
import { View, Text, StyleSheet, TextInput, TouchableOpacity, ScrollView } from 'react-native';
import { Target, Plus, X, CheckCircle } from 'lucide-react-native';
import { CheckInStepProps } from '../types';

const SUGGESTED_GOALS = {
  nl: [
    'Drink 8 glazen water',
    '30 minuten bewegen',
    'Mediteer 10 minuten',
    'Lees 20 pagina\'s',
    'Bel een vriend',
    'Ga vroeg naar bed',
    'Eet gezond ontbijt',
    'Schrijf in dagboek',
    'Doe ademhalingsoefening',
    'Luister naar muziek',
  ],
  en: [
    'Drink 8 glasses of water',
    'Exercise for 30 minutes',
    'Meditate for 10 minutes',
    'Read 20 pages',
    'Call a friend',
    'Go to bed early',
    'Eat healthy breakfast',
    'Write in journal',
    'Do breathing exercise',
    'Listen to music',
  ],
};

export const GoalsStep: React.FC<CheckInStepProps> = ({
  checkInData,
  updateCheckInData,
  colors,
  language,
}) => {
  const [newGoal, setNewGoal] = useState('');
  const suggestedGoals = SUGGESTED_GOALS[language as keyof typeof SUGGESTED_GOALS] || SUGGESTED_GOALS.en;

  const addGoal = (goal: string) => {
    if (goal.trim() && !checkInData.dailyGoals.includes(goal.trim())) {
      updateCheckInData({
        dailyGoals: [...checkInData.dailyGoals, goal.trim()],
      });
      setNewGoal('');
    }
  };

  const removeGoal = (goalToRemove: string) => {
    updateCheckInData({
      dailyGoals: checkInData.dailyGoals.filter(goal => goal !== goalToRemove),
    });
  };

  const addSuggestedGoal = (goal: string) => {
    addGoal(goal);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Target size={28} color={colors.primary} />
          <Text style={[styles.title, { color: colors.text }]}>
            {language === 'nl' ? 'Wat zijn je doelen voor vandaag?' : 'What are your goals for today?'}
          </Text>
        </View>
        <Text style={[styles.subtitle, { color: colors.muted }]}>
          {language === 'nl' 
            ? 'Stel kleine, haalbare doelen om je dag succesvol te maken'
            : 'Set small, achievable goals to make your day successful'
          }
        </Text>
      </View>

      {/* Add new goal */}
      <View style={styles.addGoalSection}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {language === 'nl' ? 'Nieuw doel toevoegen' : 'Add new goal'}
        </Text>
        <View style={styles.addGoalContainer}>
          <TextInput
            style={[
              styles.goalInput,
              {
                backgroundColor: colors.card,
                borderColor: colors.border,
                color: colors.text,
              },
            ]}
            placeholder={
              language === 'nl'
                ? 'Typ je doel hier...'
                : 'Type your goal here...'
            }
            placeholderTextColor={colors.muted}
            value={newGoal}
            onChangeText={setNewGoal}
            onSubmitEditing={() => addGoal(newGoal)}
          />
          <TouchableOpacity
            style={[styles.addButton, { backgroundColor: colors.primary }]}
            onPress={() => addGoal(newGoal)}
            disabled={!newGoal.trim()}
          >
            <Plus size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </View>

      {/* Current goals */}
      {checkInData.dailyGoals.length > 0 && (
        <View style={styles.currentGoalsSection}>
          <Text style={[styles.sectionTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Je doelen voor vandaag' : 'Your goals for today'}
          </Text>
          <View style={styles.goalsList}>
            {checkInData.dailyGoals.map((goal, index) => (
              <View
                key={index}
                style={[
                  styles.goalItem,
                  {
                    backgroundColor: colors.card,
                    borderColor: colors.border,
                  },
                ]}
              >
                <CheckCircle size={20} color={colors.success || colors.primary} />
                <Text style={[styles.goalText, { color: colors.text }]}>
                  {goal}
                </Text>
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => removeGoal(goal)}
                >
                  <X size={16} color={colors.muted} />
                </TouchableOpacity>
              </View>
            ))}
          </View>
        </View>
      )}

      {/* Suggested goals */}
      <View style={styles.suggestedGoalsSection}>
        <Text style={[styles.sectionTitle, { color: colors.text }]}>
          {language === 'nl' ? 'Voorgestelde doelen' : 'Suggested goals'}
        </Text>
        <View style={styles.suggestedGoalsList}>
          {suggestedGoals
            .filter(goal => !checkInData.dailyGoals.includes(goal))
            .slice(0, 6)
            .map((goal, index) => (
              <TouchableOpacity
                key={index}
                style={[
                  styles.suggestedGoalItem,
                  {
                    backgroundColor: colors.card,
                    borderColor: colors.border,
                  },
                ]}
                onPress={() => addSuggestedGoal(goal)}
              >
                <Plus size={16} color={colors.primary} />
                <Text style={[styles.suggestedGoalText, { color: colors.text }]}>
                  {goal}
                </Text>
              </TouchableOpacity>
            ))}
        </View>
      </View>

      {/* Motivation */}
      <View style={[styles.motivationContainer, { backgroundColor: colors.primary + '10', borderColor: colors.primary + '30' }]}>
        <Text style={[styles.motivationText, { color: colors.text }]}>
          🎯 {language === 'nl' 
            ? 'Onthoud: Kleine doelen leiden tot grote overwinningen!'
            : 'Remember: Small goals lead to big victories!'
          }
        </Text>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    marginBottom: 32,
    gap: 12,
  },
  titleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    flex: 1,
  },
  subtitle: {
    fontSize: 16,
    lineHeight: 24,
  },
  addGoalSection: {
    marginBottom: 32,
    gap: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
  },
  addGoalContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  goalInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
  },
  addButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  currentGoalsSection: {
    marginBottom: 32,
    gap: 16,
  },
  goalsList: {
    gap: 12,
  },
  goalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    gap: 12,
  },
  goalText: {
    flex: 1,
    fontSize: 16,
  },
  removeButton: {
    padding: 4,
  },
  suggestedGoalsSection: {
    marginBottom: 32,
    gap: 16,
  },
  suggestedGoalsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  suggestedGoalItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
    gap: 6,
  },
  suggestedGoalText: {
    fontSize: 14,
    fontWeight: '500',
  },
  motivationContainer: {
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 20,
  },
  motivationText: {
    fontSize: 14,
    lineHeight: 20,
    textAlign: 'center',
  },
});
