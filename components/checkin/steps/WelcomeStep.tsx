import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Heart, Sun, Moon, Coffee } from 'lucide-react-native';
import { CheckInStepProps } from '../types';

export const WelcomeStep: React.FC<CheckInStepProps> = ({
  colors,
  language,
}) => {
  const getGreeting = () => {
    const hour = new Date().getHours();
    
    if (language === 'nl') {
      if (hour < 12) return { text: 'Goedemorgen!', icon: Sun };
      if (hour < 17) return { text: 'Goedemiddag!', icon: Coffee };
      return { text: 'Go<PERSON>navond!', icon: Moon };
    } else {
      if (hour < 12) return { text: 'Good Morning!', icon: Sun };
      if (hour < 17) return { text: 'Good Afternoon!', icon: Coffee };
      return { text: 'Good Evening!', icon: Moon };
    }
  };

  const greeting = getGreeting();
  const GreetingIcon = greeting.icon;

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={[colors.primary + '20', colors.card]}
        style={styles.welcomeCard}
      >
        <View style={[styles.iconContainer, { backgroundColor: colors.primary + '20' }]}>
          <GreetingIcon size={48} color={colors.primary} />
        </View>
        
        <Text style={[styles.greeting, { color: colors.text }]}>
          {greeting.text}
        </Text>
        
        <Text style={[styles.subtitle, { color: colors.muted }]}>
          {language === 'nl' 
            ? 'Tijd voor je dagelijkse check-in. Laten we kijken hoe het met je gaat.'
            : "Time for your daily check-in. Let's see how you're doing."
          }
        </Text>

        <View style={styles.motivationContainer}>
          <Heart size={20} color={colors.primary} />
          <Text style={[styles.motivation, { color: colors.text }]}>
            {language === 'nl'
              ? 'Elke dag is een nieuwe kans om te groeien'
              : 'Every day is a new opportunity to grow'
            }
          </Text>
        </View>
      </LinearGradient>

      <View style={styles.infoContainer}>
        <Text style={[styles.infoTitle, { color: colors.text }]}>
          {language === 'nl' ? 'Wat gaan we doen?' : 'What will we do?'}
        </Text>
        
        <View style={styles.stepsList}>
          <View style={styles.stepItem}>
            <View style={[styles.stepDot, { backgroundColor: colors.primary }]} />
            <Text style={[styles.stepText, { color: colors.text }]}>
              {language === 'nl' ? 'Je stemming en verlangen bijhouden' : 'Track your mood and cravings'}
            </Text>
          </View>
          
          <View style={styles.stepItem}>
            <View style={[styles.stepDot, { backgroundColor: colors.primary }]} />
            <Text style={[styles.stepText, { color: colors.text }]}>
              {language === 'nl' ? 'Gezondheidsgegevens loggen' : 'Log health metrics'}
            </Text>
          </View>
          
          <View style={styles.stepItem}>
            <View style={[styles.stepDot, { backgroundColor: colors.primary }]} />
            <Text style={[styles.stepText, { color: colors.text }]}>
              {language === 'nl' ? 'Doelen voor vandaag stellen' : 'Set goals for today'}
            </Text>
          </View>
          
          <View style={styles.stepItem}>
            <View style={[styles.stepDot, { backgroundColor: colors.primary }]} />
            <Text style={[styles.stepText, { color: colors.text }]}>
              {language === 'nl' ? 'Reflecteren op je voortgang' : 'Reflect on your progress'}
            </Text>
          </View>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    gap: 24,
  },
  welcomeCard: {
    padding: 32,
    borderRadius: 20,
    alignItems: 'center',
    gap: 16,
  },
  iconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  greeting: {
    fontSize: 28,
    fontWeight: '700',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  motivationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginTop: 8,
  },
  motivation: {
    fontSize: 14,
    fontWeight: '500',
    fontStyle: 'italic',
  },
  infoContainer: {
    gap: 16,
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: '600',
  },
  stepsList: {
    gap: 12,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  stepDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  stepText: {
    fontSize: 16,
    flex: 1,
  },
});
