import React, { useState, useRef, useEffect } from 'react';
import {
  Modal,
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Animated,
  Dimensions,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import {
  X,
  ChevronLeft,
  ChevronRight,
  Heart,
  Target,
  Calendar,
  CheckCircle,
  Sparkles,
} from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { useUserStore } from '@/store/user/user-store';
import { useTheme } from '@/context/theme-context';
import Colors from '@/constants/colors';
import { useTranslation } from '@/hooks/useTranslation';

// Import step components (we'll create these)
// import { WelcomeStep } from './steps/WelcomeStep';
// import { MoodStep } from './steps/MoodStep';
// import { HealthStep } from './steps/HealthStep';
// import { GoalsStep } from './steps/GoalsStep';
// import { ReflectionStep } from './steps/ReflectionStep';
// import { SummaryStep } from './steps/SummaryStep';

interface DailyCheckInModalProps {
  visible: boolean;
  onClose: () => void;
  onComplete?: () => void;
}

interface CheckInData {
  mood: number;
  cravingIntensity: number;
  moodNotes: string;
  healthMetrics: {
    sleep: number;
    hydration: number;
    exercise: number;
    pills: number;
  };
  dailyGoals: string[];
  reflection: string;
  completedAt: string;
}

const STEPS = [
  { id: 'welcome', title: 'Welcome', icon: Heart },
  { id: 'mood', title: 'Mood', icon: Heart },
  { id: 'health', title: 'Health', icon: Target },
  { id: 'goals', title: 'Goals', icon: Target },
  { id: 'reflection', title: 'Reflection', icon: Calendar },
  { id: 'summary', title: 'Summary', icon: CheckCircle },
];

export const DailyCheckInModal: React.FC<DailyCheckInModalProps> = ({
  visible,
  onClose,
  onComplete,
}) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { t } = useTranslation();
  const { profile, addMoodEntry, addHealthMetric, addDailyCheckIn } = useUserStore();
  
  const [currentStep, setCurrentStep] = useState(0);
  const [checkInData, setCheckInData] = useState<CheckInData>({
    mood: 3,
    cravingIntensity: 1,
    moodNotes: '',
    healthMetrics: {
      sleep: 0,
      hydration: 0,
      exercise: 0,
      pills: 0,
    },
    dailyGoals: [],
    reflection: '',
    completedAt: new Date().toISOString(),
  });

  const scrollViewRef = useRef<ScrollView>(null);
  const progressAnim = useRef(new Animated.Value(0)).current;

  // Animation for progress bar
  useEffect(() => {
    Animated.timing(progressAnim, {
      toValue: currentStep / (STEPS.length - 1),
      duration: 300,
      useNativeDriver: false,
    }).start();
  }, [currentStep, progressAnim]);

  const handleNext = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    if (currentStep < STEPS.length - 1) {
      setCurrentStep(currentStep + 1);
      scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }

    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
      scrollViewRef.current?.scrollTo({ x: 0, y: 0, animated: true });
    }
  };

  const handleComplete = async () => {
    try {
      // Save complete daily check-in entry
      await addDailyCheckIn({
        date: new Date(checkInData.completedAt).toISOString().split('T')[0],
        mood: checkInData.mood,
        cravingIntensity: checkInData.cravingIntensity,
        moodNotes: checkInData.moodNotes,
        healthMetrics: checkInData.healthMetrics,
        dailyGoals: checkInData.dailyGoals,
        reflection: checkInData.reflection,
        completedAt: checkInData.completedAt,
      });

      // Also save mood entry for backward compatibility with existing mood tracking
      await addMoodEntry({
        date: checkInData.completedAt,
        mood: checkInData.mood as 1 | 2 | 3 | 4 | 5,
        cravingIntensity: checkInData.cravingIntensity as 1 | 2 | 3 | 4 | 5,
        notes: checkInData.moodNotes,
      });

      // Save health metrics for backward compatibility with existing health tracking
      const dateString = new Date(checkInData.completedAt).toISOString().split('T')[0];

      if (checkInData.healthMetrics.sleep > 0) {
        await addHealthMetric({
          name: 'sleep',
          value: checkInData.healthMetrics.sleep,
          unit: 'hours',
          date: dateString,
          metric: 'sleep',
          notes: '',
        });
      }

      if (checkInData.healthMetrics.hydration > 0) {
        await addHealthMetric({
          name: 'hydration',
          value: checkInData.healthMetrics.hydration,
          unit: 'glasses',
          date: dateString,
          metric: 'hydration',
          notes: '',
        });
      }

      if (checkInData.healthMetrics.exercise > 0) {
        await addHealthMetric({
          name: 'exercise',
          value: checkInData.healthMetrics.exercise,
          unit: 'minutes',
          date: dateString,
          metric: 'exercise',
          notes: '',
        });
      }

      if (checkInData.healthMetrics.pills > 0) {
        await addHealthMetric({
          name: 'pills',
          value: checkInData.healthMetrics.pills,
          unit: 'pills',
          date: dateString,
          metric: 'pills',
          notes: '',
        });
      }

      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }

      onComplete?.();
      onClose();
    } catch (error) {
      console.error('Error saving check-in data:', error);
    }
  };

  const updateCheckInData = (updates: Partial<CheckInData>) => {
    setCheckInData(prev => ({ ...prev, ...updates }));
  };

  const renderStep = () => {
    // Simplified placeholder for now
    return (
      <View style={styles.content}>
        <Text style={styles.title}>Daily Check-in</Text>
        <Text style={styles.subtitle}>
          This is a placeholder for the daily check-in flow.
        </Text>
        <TouchableOpacity
          style={styles.primaryButton}
          onPress={handleComplete}
        >
          <Text style={styles.primaryButtonText}>Complete Check-in</Text>
        </TouchableOpacity>
      </View>
    );
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={false}
      onRequestClose={onClose}
    >
      <LinearGradient
        colors={[colors.background, colors.card]}
        style={styles.container}
      >
        {/* Header */}
        <View style={[styles.header, { borderBottomColor: colors.border }]}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color={colors.text} />
          </TouchableOpacity>
          
          <View style={styles.headerCenter}>
            <Text style={[styles.headerTitle, { color: colors.text }]}>
              {profile?.language === 'nl' ? 'Dagelijkse Check-in' : 'Daily Check-in'}
            </Text>
            <Text style={[styles.stepIndicator, { color: colors.muted }]}>
              {currentStep + 1} {profile?.language === 'nl' ? 'van' : 'of'} {STEPS.length}
            </Text>
          </View>

          <View style={styles.headerRight}>
            <Sparkles size={24} color={colors.primary} />
          </View>
        </View>

        {/* Progress Bar */}
        <View style={[styles.progressContainer, { backgroundColor: colors.border }]}>
          <Animated.View
            style={[
              styles.progressBar,
              {
                backgroundColor: colors.primary,
                width: progressAnim.interpolate({
                  inputRange: [0, 1],
                  outputRange: ['0%', '100%'],
                }),
              },
            ]}
          />
        </View>

        {/* Content */}
        <ScrollView
          ref={scrollViewRef}
          style={styles.content}
          contentContainerStyle={styles.contentContainer}
          showsVerticalScrollIndicator={false}
        >
          {renderStep()}
        </ScrollView>

        {/* Navigation */}
        <View style={[styles.navigation, { borderTopColor: colors.border }]}>
          <TouchableOpacity
            onPress={handlePrevious}
            style={[
              styles.navButton,
              { 
                backgroundColor: currentStep > 0 ? colors.card : 'transparent',
                borderColor: colors.border,
              },
            ]}
            disabled={currentStep === 0}
          >
            <ChevronLeft 
              size={20} 
              color={currentStep > 0 ? colors.text : colors.muted} 
            />
            <Text style={[
              styles.navButtonText,
              { color: currentStep > 0 ? colors.text : colors.muted }
            ]}>
              {profile?.language === 'nl' ? 'Vorige' : 'Previous'}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={handleNext}
            style={[styles.navButton, { backgroundColor: colors.primary }]}
          >
            <Text style={[styles.navButtonText, { color: '#fff' }]}>
              {currentStep === STEPS.length - 1
                ? (profile?.language === 'nl' ? 'Voltooien' : 'Complete')
                : (profile?.language === 'nl' ? 'Volgende' : 'Next')
              }
            </Text>
            <ChevronRight size={20} color="#fff" />
          </TouchableOpacity>
        </View>
      </LinearGradient>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'ios' ? 60 : 40,
    paddingBottom: 20,
    borderBottomWidth: 1,
  },
  closeButton: {
    padding: 8,
    borderRadius: 20,
  },
  headerCenter: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    marginBottom: 4,
  },
  stepIndicator: {
    fontSize: 14,
    fontWeight: '500',
  },
  headerRight: {
    width: 40,
    alignItems: 'center',
  },
  progressContainer: {
    height: 4,
    marginHorizontal: 20,
    borderRadius: 2,
    overflow: 'hidden',
  },
  progressBar: {
    height: '100%',
    borderRadius: 2,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
    minHeight: Dimensions.get('window').height * 0.6,
  },
  navigation: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 20,
    paddingBottom: Platform.OS === 'ios' ? 40 : 20,
    borderTopWidth: 1,
    gap: 12,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    flex: 1,
    gap: 8,
  },
  navButtonText: {
    fontSize: 16,
    fontWeight: '600',
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    textAlign: 'center',
    marginBottom: 16,
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 32,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    marginTop: 24,
  },
  primaryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});
