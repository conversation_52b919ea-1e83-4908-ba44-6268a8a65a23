import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Calendar, Heart, Target, BookOpen, Clock } from 'lucide-react-native';
import { useUserStore } from '@/store/user/user-store';
import { useTheme } from '@/context/theme-context';
import Colors from '@/constants/colors';

interface CheckInHistoryProps {
  onClose?: () => void;
}

const MOOD_EMOJIS = ['😢', '😕', '😐', '😊', '😄'];
const MOOD_LABELS = {
  nl: ['Slecht', 'Matig', 'Oké', 'Goed', 'Uitstekend'],
  en: ['Poor', 'Fair', 'Okay', 'Good', 'Excellent'],
};

export const CheckInHistory: React.FC<CheckInHistoryProps> = ({ onClose }) => {
  const { currentTheme } = useTheme();
  const colors = Colors[currentTheme];
  const { profile } = useUserStore();
  
  const dailyCheckIns = profile?.dailyCheckIns || [];
  const language = profile?.language || 'en';
  const moodLabels = MOOD_LABELS[language as keyof typeof MOOD_LABELS] || MOOD_LABELS.en;

  // Sort check-ins by date (most recent first)
  const sortedCheckIns = [...dailyCheckIns].sort((a, b) => 
    new Date(b.completedAt).getTime() - new Date(a.completedAt).getTime()
  );

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(language === 'nl' ? 'nl-NL' : 'en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  if (dailyCheckIns.length === 0) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <View style={styles.emptyState}>
          <Calendar size={64} color={colors.muted} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>
            {language === 'nl' ? 'Nog geen check-ins' : 'No check-ins yet'}
          </Text>
          <Text style={[styles.emptySubtitle, { color: colors.muted }]}>
            {language === 'nl' 
              ? 'Je dagelijkse check-ins verschijnen hier zodra je ze hebt voltooid'
              : 'Your daily check-ins will appear here once you complete them'
            }
          </Text>
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <Text style={[styles.title, { color: colors.text }]}>
          {language === 'nl' ? 'Check-in Geschiedenis' : 'Check-in History'}
        </Text>
        <Text style={[styles.subtitle, { color: colors.muted }]}>
          {sortedCheckIns.length} {language === 'nl' ? 'check-ins voltooid' : 'check-ins completed'}
        </Text>
      </View>

      <View style={styles.checkInsList}>
        {sortedCheckIns.map((checkIn) => (
          <View key={checkIn.id} style={[styles.checkInCard, { backgroundColor: colors.card, borderColor: colors.border }]}>
            {/* Header */}
            <View style={styles.cardHeader}>
              <View style={styles.dateContainer}>
                <Text style={[styles.dateText, { color: colors.text }]}>
                  {formatDate(checkIn.completedAt)}
                </Text>
                <View style={styles.timeContainer}>
                  <Clock size={14} color={colors.muted} />
                  <Text style={[styles.timeText, { color: colors.muted }]}>
                    {formatTime(checkIn.completedAt)}
                  </Text>
                </View>
              </View>
            </View>

            {/* Mood */}
            <View style={styles.section}>
              <View style={styles.sectionHeader}>
                <Heart size={16} color={colors.primary} />
                <Text style={[styles.sectionTitle, { color: colors.text }]}>
                  {language === 'nl' ? 'Stemming' : 'Mood'}
                </Text>
              </View>
              <View style={styles.moodDisplay}>
                <Text style={styles.moodEmoji}>{MOOD_EMOJIS[checkIn.mood - 1]}</Text>
                <Text style={[styles.moodText, { color: colors.text }]}>
                  {moodLabels[checkIn.mood - 1]}
                </Text>
              </View>
              {checkIn.moodNotes && (
                <Text style={[styles.notes, { color: colors.muted }]}>
                  "{checkIn.moodNotes}"
                </Text>
              )}
            </View>

            {/* Health Metrics */}
            {Object.values(checkIn.healthMetrics).some(value => value > 0) && (
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Target size={16} color={colors.primary} />
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    {language === 'nl' ? 'Gezondheid' : 'Health'}
                  </Text>
                </View>
                <View style={styles.healthMetrics}>
                  {checkIn.healthMetrics.sleep > 0 && (
                    <Text style={[styles.metricText, { color: colors.text }]}>
                      💤 {checkIn.healthMetrics.sleep} {language === 'nl' ? 'uren slaap' : 'hours sleep'}
                    </Text>
                  )}
                  {checkIn.healthMetrics.hydration > 0 && (
                    <Text style={[styles.metricText, { color: colors.text }]}>
                      💧 {checkIn.healthMetrics.hydration} {language === 'nl' ? 'glazen water' : 'glasses water'}
                    </Text>
                  )}
                  {checkIn.healthMetrics.exercise > 0 && (
                    <Text style={[styles.metricText, { color: colors.text }]}>
                      🏃 {checkIn.healthMetrics.exercise} {language === 'nl' ? 'min beweging' : 'min exercise'}
                    </Text>
                  )}
                  {checkIn.healthMetrics.pills > 0 && (
                    <Text style={[styles.metricText, { color: colors.text }]}>
                      💊 {checkIn.healthMetrics.pills} {language === 'nl' ? 'pillen' : 'pills'}
                    </Text>
                  )}
                </View>
              </View>
            )}

            {/* Goals */}
            {checkIn.dailyGoals.length > 0 && (
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Target size={16} color={colors.primary} />
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    {language === 'nl' ? 'Doelen' : 'Goals'}
                  </Text>
                </View>
                <View style={styles.goalsList}>
                  {checkIn.dailyGoals.map((goal, index) => (
                    <Text key={index} style={[styles.goalText, { color: colors.text }]}>
                      • {goal}
                    </Text>
                  ))}
                </View>
              </View>
            )}

            {/* Reflection */}
            {checkIn.reflection && (
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <BookOpen size={16} color={colors.primary} />
                  <Text style={[styles.sectionTitle, { color: colors.text }]}>
                    {language === 'nl' ? 'Reflectie' : 'Reflection'}
                  </Text>
                </View>
                <Text style={[styles.reflectionText, { color: colors.text }]}>
                  {checkIn.reflection}
                </Text>
              </View>
            )}
          </View>
        ))}
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
  },
  header: {
    marginBottom: 24,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginTop: 16,
    marginBottom: 8,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    textAlign: 'center',
    lineHeight: 24,
  },
  checkInsList: {
    gap: 16,
  },
  checkInCard: {
    borderRadius: 16,
    borderWidth: 1,
    padding: 20,
    gap: 16,
  },
  cardHeader: {
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.1)',
    paddingBottom: 12,
  },
  dateContainer: {
    gap: 4,
  },
  dateText: {
    fontSize: 18,
    fontWeight: '600',
  },
  timeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  timeText: {
    fontSize: 14,
  },
  section: {
    gap: 8,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
  },
  moodDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  moodEmoji: {
    fontSize: 24,
  },
  moodText: {
    fontSize: 16,
    fontWeight: '500',
  },
  notes: {
    fontSize: 14,
    fontStyle: 'italic',
    marginTop: 4,
  },
  healthMetrics: {
    gap: 4,
  },
  metricText: {
    fontSize: 14,
  },
  goalsList: {
    gap: 4,
  },
  goalText: {
    fontSize: 14,
  },
  reflectionText: {
    fontSize: 14,
    lineHeight: 20,
  },
});
