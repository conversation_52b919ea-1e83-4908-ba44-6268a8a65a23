export interface CheckInData {
  mood: number;
  cravingIntensity: number;
  moodNotes: string;
  healthMetrics: {
    sleep: number;
    hydration: number;
    exercise: number;
    pills: number;
  };
  dailyGoals: string[];
  reflection: string;
  completedAt: string;
}

export interface ColorScheme {
  background: string;
  card: string;
  border: string;
  text: string;
  primary: string;
  muted: string;
  success?: string;
  danger?: string;
  info?: string;
  warning?: string;
  accent?: string;
}

export interface CheckInStepProps {
  checkInData: CheckInData;
  updateCheckInData: (updates: Partial<CheckInData>) => void;
  colors: ColorScheme;
  language: string;
  onNext?: () => void;
  onPrevious?: () => void;
  onComplete?: () => void;
}
